import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Plus, 
  Search, 
  RefreshCw,
  Calendar,
  Target as TargetIcon,
  Loader2,
  Eye,
  Trash2
} from 'lucide-react';

interface Report {
  report_id: string;
  template_name: string;
  generation_date: string;
  status: 'processing' | 'completed' | 'failed';
  download: {
    available: boolean;
    url?: string;
  };
  source: {
    list_type: string;
    id_list: string[];
    description: string;
  };
  target_info?: {
    address: string;
    description: string;
  };
}

interface ReportsPageProps {
  showCreateModal: boolean;
  setShowCreateModal: (show: boolean) => void;
}

const ReportsPage: React.FC<ReportsPageProps> = ({ showCreateModal, setShowCreateModal }) => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    setLoading(true);
    // Simulate API call with mock data
    setTimeout(() => {
      setReports([
        {
          report_id: '1',
          template_name: 'Executive Summary',
          generation_date: new Date().toISOString(),
          status: 'completed',
          download: {
            available: true,
            url: '/reports/download/exec-summary-1.pdf'
          },
          source: {
            list_type: 'targets',
            id_list: ['target-1'],
            description: 'Main website scan results'
          },
          target_info: {
            address: 'https://example.com',
            description: 'Main website'
          }
        },
        {
          report_id: '2',
          template_name: 'Detailed Technical Report',
          generation_date: new Date(Date.now() - 86400000).toISOString(),
          status: 'completed',
          download: {
            available: true,
            url: '/reports/download/tech-report-2.pdf'
          },
          source: {
            list_type: 'scans',
            id_list: ['scan-1', 'scan-2'],
            description: 'Multiple scan results'
          }
        },
        {
          report_id: '3',
          template_name: 'Compliance Report',
          generation_date: new Date().toISOString(),
          status: 'processing',
          download: {
            available: false
          },
          source: {
            list_type: 'targets',
            id_list: ['target-2'],
            description: 'Compliance scan results'
          },
          target_info: {
            address: 'https://demo.testfire.net',
            description: 'Demo application'
          }
        }
      ]);
      setLoading(false);
    }, 1000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'completed':
        return <FileText className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <FileText className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-blue-600 bg-blue-100';
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleDownload = (report: Report) => {
    if (report.download.available && report.download.url) {
      // In a real app, this would trigger the download
      console.log('Downloading report:', report.download.url);
    }
  };

  const handleDelete = async (reportId: string) => {
    if (!confirm('Are you sure you want to delete this report?')) return;
    
    setReports(prev => prev.filter(r => r.report_id !== reportId));
  };

  const filteredReports = reports.filter(report =>
    report.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.source.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (report.target_info?.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-4">
      {/* Search and Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search reports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={loadReports}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center gap-1.5"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Reports List */}
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 content-stable ${loading ? 'loading-overlay loading' : ''}`}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            <span className="ml-2 text-sm text-gray-600">Loading reports...</span>
          </div>
        ) : filteredReports.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-10 h-10 text-gray-400 mx-auto mb-3" />
            <h3 className="text-base font-medium text-gray-900 mb-2">No reports found</h3>
            <p className="text-sm text-gray-600 mb-3">
              {searchTerm ? 'No reports match your search criteria.' : 'Generate your first security report.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Generate Report
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredReports.map((report) => (
              <div key={report.report_id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(report.status)}
                      <h3 className="text-lg font-medium text-gray-900">{report.template_name}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(report.status)}`}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{report.source.description}</p>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>Generated: {formatDate(report.generation_date)}</span>
                      </div>
                      
                      {report.target_info && (
                        <div className="flex items-center gap-1">
                          <TargetIcon className="w-4 h-4" />
                          <span>{report.target_info.address}</span>
                        </div>
                      )}
                      
                      <span>Source: {report.source.list_type} ({report.source.id_list.length} items)</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {report.status === 'completed' && report.download.available && (
                      <>
                        <button
                          onClick={() => handleDownload(report)}
                          className="text-blue-600 hover:text-blue-800 p-2 rounded-md hover:bg-blue-50"
                          title="Download report"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                        <button
                          className="text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-50"
                          title="Preview report"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => handleDelete(report.report_id)}
                      className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-50"
                      title="Delete report"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create Report Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={() => setShowCreateModal(false)} />
            
            <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Generate New Report</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Report Template
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>Executive Summary</option>
                    <option>Detailed Technical Report</option>
                    <option>Compliance Report</option>
                    <option>Quick Report</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Source Type
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>Targets</option>
                    <option>Scans</option>
                    <option>Vulnerabilities</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    placeholder="Report description"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Generate Report
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportsPage;
