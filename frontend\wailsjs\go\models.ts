export namespace acunetix {
	
	export class ConnectionStatus {
	    connected: boolean;
	    message: string;
	    version?: string;
	    build?: string;
	
	    static createFrom(source: any = {}) {
	        return new ConnectionStatus(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.connected = source["connected"];
	        this.message = source["message"];
	        this.version = source["version"];
	        this.build = source["build"];
	    }
	}
	export class Link {
	    rel: string;
	    href: string;
	
	    static createFrom(source: any = {}) {
	        return new Link(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.rel = source["rel"];
	        this.href = source["href"];
	    }
	}
	export class Schedule {
	    disable: boolean;
	    start_date: string;
	    time_sensitive: boolean;
	
	    static createFrom(source: any = {}) {
	        return new Schedule(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.disable = source["disable"];
	        this.start_date = source["start_date"];
	        this.time_sensitive = source["time_sensitive"];
	    }
	}
	export class NewScan {
	    target_id: string;
	    profile_id: string;
	    schedule: Schedule;
	    ui_session_id?: string;
	    report_template_id?: string[];
	
	    static createFrom(source: any = {}) {
	        return new NewScan(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.target_id = source["target_id"];
	        this.profile_id = source["profile_id"];
	        this.schedule = this.convertValues(source["schedule"], Schedule);
	        this.ui_session_id = source["ui_session_id"];
	        this.report_template_id = source["report_template_id"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class NewTarget {
	    address: string;
	    description: string;
	    criticality: number;
	    type: string;
	
	    static createFrom(source: any = {}) {
	        return new NewTarget(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.address = source["address"];
	        this.description = source["description"];
	        this.criticality = source["criticality"];
	        this.type = source["type"];
	    }
	}
	export class Pagination {
	    count: number;
	    cursor_hash: string;
	    next_cursor: string;
	    sort: string;
	
	    static createFrom(source: any = {}) {
	        return new Pagination(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.count = source["count"];
	        this.cursor_hash = source["cursor_hash"];
	        this.next_cursor = source["next_cursor"];
	        this.sort = source["sort"];
	    }
	}
	export class ScanAuthorization {
	    url: string;
	    content: string;
	
	    static createFrom(source: any = {}) {
	        return new ScanAuthorization(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.content = source["content"];
	    }
	}
	export class Target {
	    target_id: string;
	    address: string;
	    description: string;
	    criticality: number;
	    type: string;
	    last_scan_date?: string;
	    last_scan_id?: string;
	    last_scan_session_id?: string;
	    last_scan_session_status?: string;
	    severity_counts: SeverityCounts;
	    threat: number;
	    threat_score: number;
	    scan_authorization?: ScanAuthorization;
	    continuous_mode: boolean;
	    links?: Link[];
	    manual_intervention: boolean;
	    verification?: string;
	    fqdn?: string;
	    fqdn_hash?: string;
	    fqdn_status?: string;
	    fqdn_tm_hash?: string;
	    deleted_at?: string;
	    default_scanning_profile_id?: string;
	    default_overrides?: string;
	    added_date?: string;
	
	    static createFrom(source: any = {}) {
	        return new Target(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.target_id = source["target_id"];
	        this.address = source["address"];
	        this.description = source["description"];
	        this.criticality = source["criticality"];
	        this.type = source["type"];
	        this.last_scan_date = source["last_scan_date"];
	        this.last_scan_id = source["last_scan_id"];
	        this.last_scan_session_id = source["last_scan_session_id"];
	        this.last_scan_session_status = source["last_scan_session_status"];
	        this.severity_counts = this.convertValues(source["severity_counts"], SeverityCounts);
	        this.threat = source["threat"];
	        this.threat_score = source["threat_score"];
	        this.scan_authorization = this.convertValues(source["scan_authorization"], ScanAuthorization);
	        this.continuous_mode = source["continuous_mode"];
	        this.links = this.convertValues(source["links"], Link);
	        this.manual_intervention = source["manual_intervention"];
	        this.verification = source["verification"];
	        this.fqdn = source["fqdn"];
	        this.fqdn_hash = source["fqdn_hash"];
	        this.fqdn_status = source["fqdn_status"];
	        this.fqdn_tm_hash = source["fqdn_tm_hash"];
	        this.deleted_at = source["deleted_at"];
	        this.default_scanning_profile_id = source["default_scanning_profile_id"];
	        this.default_overrides = source["default_overrides"];
	        this.added_date = source["added_date"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class SeverityCounts {
	    critical: number;
	    high: number;
	    medium: number;
	    low: number;
	    info: number;
	
	    static createFrom(source: any = {}) {
	        return new SeverityCounts(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.critical = source["critical"];
	        this.high = source["high"];
	        this.medium = source["medium"];
	        this.low = source["low"];
	        this.info = source["info"];
	    }
	}
	export class ScanSession {
	    scan_session_id: string;
	    status: string;
	    start_date: string;
	    end_date?: string;
	    progress: number;
	    threat_score: number;
	    threat: number;
	    event_level: number;
	    severity_counts: SeverityCounts;
	    acusensor: boolean;
	
	    static createFrom(source: any = {}) {
	        return new ScanSession(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.scan_session_id = source["scan_session_id"];
	        this.status = source["status"];
	        this.start_date = source["start_date"];
	        this.end_date = source["end_date"];
	        this.progress = source["progress"];
	        this.threat_score = source["threat_score"];
	        this.threat = source["threat"];
	        this.event_level = source["event_level"];
	        this.severity_counts = this.convertValues(source["severity_counts"], SeverityCounts);
	        this.acusensor = source["acusensor"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Scan {
	    scan_id: string;
	    target_id: string;
	    profile_id: string;
	    profile_name: string;
	    ui_session_id: string;
	    report_template_id: string[];
	    max_scan_time: number;
	    incremental: boolean;
	    status: string;
	    start_date?: string;
	    next_run?: string;
	    schedule: Schedule;
	    current_session: ScanSession;
	    previous_session?: ScanSession;
	    target?: Target;
	    criticality: number;
	    manual_intervention: boolean;
	
	    static createFrom(source: any = {}) {
	        return new Scan(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.scan_id = source["scan_id"];
	        this.target_id = source["target_id"];
	        this.profile_id = source["profile_id"];
	        this.profile_name = source["profile_name"];
	        this.ui_session_id = source["ui_session_id"];
	        this.report_template_id = source["report_template_id"];
	        this.max_scan_time = source["max_scan_time"];
	        this.incremental = source["incremental"];
	        this.status = source["status"];
	        this.start_date = source["start_date"];
	        this.next_run = source["next_run"];
	        this.schedule = this.convertValues(source["schedule"], Schedule);
	        this.current_session = this.convertValues(source["current_session"], ScanSession);
	        this.previous_session = this.convertValues(source["previous_session"], ScanSession);
	        this.target = this.convertValues(source["target"], Target);
	        this.criticality = source["criticality"];
	        this.manual_intervention = source["manual_intervention"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	export class ScanCheck {
	    check_id: string;
	    enabled: boolean;
	
	    static createFrom(source: any = {}) {
	        return new ScanCheck(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.check_id = source["check_id"];
	        this.enabled = source["enabled"];
	    }
	}
	export class ScanListResponse {
	    scans: Scan[];
	    pagination: Pagination;
	
	    static createFrom(source: any = {}) {
	        return new ScanListResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.scans = this.convertValues(source["scans"], Scan);
	        this.pagination = this.convertValues(source["pagination"], Pagination);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ScanProfile {
	    profile_id: string;
	    name: string;
	    custom: boolean;
	    checks: ScanCheck[];
	
	    static createFrom(source: any = {}) {
	        return new ScanProfile(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.profile_id = source["profile_id"];
	        this.name = source["name"];
	        this.custom = source["custom"];
	        this.checks = this.convertValues(source["checks"], ScanCheck);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ScanProfileListResponse {
	    scanning_profiles: ScanProfile[];
	
	    static createFrom(source: any = {}) {
	        return new ScanProfileListResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.scanning_profiles = this.convertValues(source["scanning_profiles"], ScanProfile);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	
	
	export class TargetListResponse {
	    targets: Target[];
	    pagination: Pagination;
	
	    static createFrom(source: any = {}) {
	        return new TargetListResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.targets = this.convertValues(source["targets"], Target);
	        this.pagination = this.convertValues(source["pagination"], Pagination);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Vulnerability {
	    vuln_id: string;
	    vt_name: string;
	    severity: number;
	    confidence: number;
	    affects_url: string;
	    affects_detail: string;
	    target_id: string;
	    target_description: string;
	    status: string;
	    last_seen: string;
	    tags: string[];
	    criticality: number;
	    app: string;
	    archived: boolean;
	    continuous: boolean;
	    issue_id: string;
	    issue_tracker_id: string;
	    plugin_instance_id: string;
	    vt_id: string;
	    vt_created: string;
	    vt_updated: string;
	    api_operation_id: string;
	    api_type: string;
	
	    static createFrom(source: any = {}) {
	        return new Vulnerability(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.vuln_id = source["vuln_id"];
	        this.vt_name = source["vt_name"];
	        this.severity = source["severity"];
	        this.confidence = source["confidence"];
	        this.affects_url = source["affects_url"];
	        this.affects_detail = source["affects_detail"];
	        this.target_id = source["target_id"];
	        this.target_description = source["target_description"];
	        this.status = source["status"];
	        this.last_seen = source["last_seen"];
	        this.tags = source["tags"];
	        this.criticality = source["criticality"];
	        this.app = source["app"];
	        this.archived = source["archived"];
	        this.continuous = source["continuous"];
	        this.issue_id = source["issue_id"];
	        this.issue_tracker_id = source["issue_tracker_id"];
	        this.plugin_instance_id = source["plugin_instance_id"];
	        this.vt_id = source["vt_id"];
	        this.vt_created = source["vt_created"];
	        this.vt_updated = source["vt_updated"];
	        this.api_operation_id = source["api_operation_id"];
	        this.api_type = source["api_type"];
	    }
	}
	export class VulnerabilityListResponse {
	    vulnerabilities: Vulnerability[];
	    pagination: Pagination;
	
	    static createFrom(source: any = {}) {
	        return new VulnerabilityListResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.vulnerabilities = this.convertValues(source["vulnerabilities"], Vulnerability);
	        this.pagination = this.convertValues(source["pagination"], Pagination);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

export namespace config {
	
	export class AcunetixConfig {
	    baseUrl: string;
	    apiKey: string;
	    insecureSkipVerify: boolean;
	    timeout: number;
	
	    static createFrom(source: any = {}) {
	        return new AcunetixConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.baseUrl = source["baseUrl"];
	        this.apiKey = source["apiKey"];
	        this.insecureSkipVerify = source["insecureSkipVerify"];
	        this.timeout = source["timeout"];
	    }
	}
	export class ProxyConfig {
	    enabled: boolean;
	    type: string;
	    host: string;
	    port: number;
	    username: string;
	    password: string;
	
	    static createFrom(source: any = {}) {
	        return new ProxyConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.enabled = source["enabled"];
	        this.type = source["type"];
	        this.host = source["host"];
	        this.port = source["port"];
	        this.username = source["username"];
	        this.password = source["password"];
	    }
	}
	export class AppConfig {
	    Acunetix: AcunetixConfig;
	    Proxy: ProxyConfig;
	
	    static createFrom(source: any = {}) {
	        return new AppConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Acunetix = this.convertValues(source["Acunetix"], AcunetixConfig);
	        this.Proxy = this.convertValues(source["Proxy"], ProxyConfig);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

