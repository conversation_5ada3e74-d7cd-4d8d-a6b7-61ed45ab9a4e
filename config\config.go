package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// AppConfig represents the complete application configuration
type AppConfig struct {
	Acunetix AcunetixConfig `yaml:"acunetix"`
	Proxy    ProxyConfig    `yaml:"proxy"`
}

// AcunetixConfig holds Acunetix API configuration
type AcunetixConfig struct {
	BaseURL            string `yaml:"base_url" json:"baseUrl"`
	APIKey             string `yaml:"api_key" json:"apiKey"`
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify" json:"insecureSkipVerify"`
	Timeout            int    `yaml:"timeout" json:"timeout"`
}

// ProxyConfig holds proxy configuration
type ProxyConfig struct {
	Enabled  bool   `yaml:"enabled" json:"enabled"`
	Type     string `yaml:"type" json:"type"` // "http", "socks5", "none"
	Host     string `yaml:"host" json:"host"`
	Port     int    `yaml:"port" json:"port"`
	Username string `yaml:"username" json:"username"`
	Password string `yaml:"password" json:"password"`
}

// ConfigManager handles configuration file operations
type ConfigManager struct {
	configPath string
	config     *AppConfig
}

// NewConfigManager creates a new configuration manager
func NewConfigManager() *ConfigManager {
	// Get executable directory
	execPath, err := os.Executable()
	if err != nil {
		execPath = "."
	}
	execDir := filepath.Dir(execPath)
	configPath := filepath.Join(execDir, "config.yaml")

	return &ConfigManager{
		configPath: configPath,
		config: &AppConfig{
			Acunetix: AcunetixConfig{
				BaseURL:            "https://127.0.0.1:3443/api/v1",
				APIKey:             "",
				InsecureSkipVerify: true,
				Timeout:            30,
			},
			Proxy: ProxyConfig{
				Enabled:  false,
				Type:     "none",
				Host:     "",
				Port:     0,
				Username: "",
				Password: "",
			},
		},
	}
}

// LoadConfig loads configuration from YAML file
func (cm *ConfigManager) LoadConfig() (*AppConfig, error) {
	// Check if config file exists
	if _, err := os.Stat(cm.configPath); os.IsNotExist(err) {
		// Create default config file
		if err := cm.SaveConfig(cm.config); err != nil {
			return nil, fmt.Errorf("failed to create default config: %w", err)
		}
		return cm.config, nil
	}

	// Read config file
	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse YAML
	var config AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	cm.config = &config
	return &config, nil
}

// SaveConfig saves configuration to YAML file
func (cm *ConfigManager) SaveConfig(config *AppConfig) error {
	// Marshal to YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// Ensure directory exists
	if err := os.MkdirAll(filepath.Dir(cm.configPath), 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Write to file
	if err := os.WriteFile(cm.configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	cm.config = config
	return nil
}

// GetConfig returns current configuration
func (cm *ConfigManager) GetConfig() *AppConfig {
	return cm.config
}

// GetConfigPath returns the configuration file path
func (cm *ConfigManager) GetConfigPath() string {
	return cm.configPath
}

// UpdateAcunetixConfig updates only the Acunetix configuration
func (cm *ConfigManager) UpdateAcunetixConfig(acunetixConfig AcunetixConfig) error {
	cm.config.Acunetix = acunetixConfig
	return cm.SaveConfig(cm.config)
}

// UpdateProxyConfig updates only the proxy configuration
func (cm *ConfigManager) UpdateProxyConfig(proxyConfig ProxyConfig) error {
	cm.config.Proxy = proxyConfig
	return cm.SaveConfig(cm.config)
}
