import React, { useState, useEffect } from 'react';
import { <PERSON>ting<PERSON>, Check, X, Alert<PERSON>ircle, Loader2, Save, Globe, Shield } from 'lucide-react';
import {
  GetAcunetixConfig,
  GetProxyConfig,
  SetAcunetixConfig,
  SetProxyConfig,
  TestConnection,
  GetConfigPath
} from '../../wailsjs/go/main/App';

interface AcunetixConfig {
  baseUrl: string;
  apiKey: string;
  insecureSkipVerify: boolean;
  timeout: number;
}

interface ProxyConfig {
  enabled: boolean;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
}

interface AppConfig {
  acunetix: AcunetixConfig;
  proxy: ProxyConfig;
}

interface ConnectionStatus {
  connected: boolean;
  message: string;
  version?: string;
  build?: string;
}

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'acunetix' | 'proxy'>('acunetix');
  const [acunetixConfig, setAcunetixConfig] = useState<AcunetixConfig>({
    baseUrl: 'https://127.0.0.1:3443/api/v1',
    apiKey: '',
    insecureSkipVerify: true,
    timeout: 30,
  });
  
  const [proxyConfig, setProxyConfig] = useState<ProxyConfig>({
    enabled: false,
    type: 'none',
    host: '',
    port: 0,
    username: '',
    password: '',
  });
  
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [configPath, setConfigPath] = useState<string>('');
  const [saveMessage, setSaveMessage] = useState<string>('');

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      // Load Acunetix configuration
      const acunetixConf = await GetAcunetixConfig();
      setAcunetixConfig({
        baseUrl: acunetixConf.baseUrl,
        apiKey: acunetixConf.apiKey,
        insecureSkipVerify: acunetixConf.insecureSkipVerify,
        timeout: acunetixConf.timeout,
      });

      // Load proxy configuration
      const proxyConf = await GetProxyConfig();
      setProxyConfig({
        enabled: proxyConf.enabled,
        type: proxyConf.type,
        host: proxyConf.host,
        port: proxyConf.port,
        username: proxyConf.username,
        password: proxyConf.password,
      });

      // Get config file path
      const path = await GetConfigPath();
      setConfigPath(path);
    } catch (error) {
      console.error('Failed to load configs:', error);
    }
  };

  const handleSaveAcunetixConfig = async () => {
    setIsSaving(true);
    setSaveMessage('');
    try {
      await SetAcunetixConfig(acunetixConfig);
      setConnectionStatus(null); // Reset connection status when config changes
      setSaveMessage('Acunetix configuration saved successfully!');
      setTimeout(() => setSaveMessage(''), 3000); // Clear message after 3 seconds
    } catch (error) {
      console.error('Failed to save Acunetix config:', error);
      setSaveMessage(`Failed to save configuration: ${error}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveProxyConfig = async () => {
    setIsSaving(true);
    setSaveMessage('');
    try {
      await SetProxyConfig(proxyConfig);
      setSaveMessage('Proxy configuration saved successfully!');
      setTimeout(() => setSaveMessage(''), 3000); // Clear message after 3 seconds
    } catch (error) {
      console.error('Failed to save proxy config:', error);
      setSaveMessage(`Failed to save proxy configuration: ${error}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    setIsLoading(true);
    try {
      // Save config first, then test connection
      await SetAcunetixConfig(acunetixConfig);
      const status = await TestConnection();
      setConnectionStatus(status);
    } catch (error) {
      setConnectionStatus({
        connected: false,
        message: `Connection failed: ${error}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
    }
    if (!connectionStatus) {
      return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
    return connectionStatus.connected ? (
      <Check className="w-4 h-4 text-green-500" />
    ) : (
      <X className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusMessage = () => {
    if (isLoading) return 'Testing connection...';
    if (!connectionStatus) return 'Not tested';
    return connectionStatus.message;
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-blue-600';
    if (!connectionStatus) return 'text-gray-500';
    return connectionStatus.connected ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-4">
      {/* Configuration File Path */}
      {configPath && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800 whitespace-nowrap">
              Configuration saved to: <code className="bg-blue-100 px-1 rounded">{configPath}</code>
            </span>
          </div>
        </div>
      )}

      {/* Save Message */}
      {saveMessage && (
        <div className={`border rounded-lg p-4 ${
          saveMessage.includes('Failed') || saveMessage.includes('failed')
            ? 'bg-red-50 border-red-200'
            : 'bg-green-50 border-green-200'
        }`}>
          <div className="flex items-center gap-2">
            {saveMessage.includes('Failed') || saveMessage.includes('failed') ? (
              <X className="w-4 h-4 text-red-600" />
            ) : (
              <Check className="w-4 h-4 text-green-600" />
            )}
            <span className={`text-sm ${
              saveMessage.includes('Failed') || saveMessage.includes('failed')
                ? 'text-red-800'
                : 'text-green-800'
            }`}>
              {saveMessage}
            </span>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('acunetix')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'acunetix'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                Acunetix Configuration
              </div>
            </button>
            <button
              onClick={() => setActiveTab('proxy')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'proxy'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4" />
                Proxy Configuration
              </div>
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'acunetix' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Acunetix Scanner Configuration</h3>
              
              <div>
                <label htmlFor="baseUrl" className="block text-sm font-medium text-gray-700 mb-1">
                  Base URL
                  <span className="text-xs text-gray-500 ml-2">
                    The base URL of your Acunetix Scanner API
                  </span>
                </label>
                <input
                  type="text"
                  id="baseUrl"
                  value={acunetixConfig.baseUrl}
                  onChange={(e) => setAcunetixConfig({ ...acunetixConfig, baseUrl: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://127.0.0.1:3443/api/v1"
                />
              </div>

              <div>
                <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 mb-1">
                  API Key
                  <span className="text-xs text-gray-500 ml-2">
                    Your Acunetix Scanner API authentication key
                  </span>
                </label>
                <input
                  type="password"
                  id="apiKey"
                  value={acunetixConfig.apiKey}
                  onChange={(e) => setAcunetixConfig({ ...acunetixConfig, apiKey: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your API key"
                />
              </div>

              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <label htmlFor="timeout" className="block text-sm font-medium text-gray-700 mb-1">
                    Timeout (seconds)
                  </label>
                  <input
                    type="number"
                    id="timeout"
                    value={acunetixConfig.timeout}
                    onChange={(e) => setAcunetixConfig({ ...acunetixConfig, timeout: parseInt(e.target.value) || 30 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="5"
                    max="300"
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="insecureSkipVerify"
                    checked={acunetixConfig.insecureSkipVerify}
                    onChange={(e) => setAcunetixConfig({ ...acunetixConfig, insecureSkipVerify: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="insecureSkipVerify" className="ml-2 text-sm text-gray-700">
                    Skip SSL verification
                  </label>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Connection Status</span>
                  <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    <span className={`text-sm ${getStatusColor()}`}>
                      {getStatusMessage()}
                    </span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveAcunetixConfig}
                    disabled={isSaving}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Save Configuration
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={handleTestConnection}
                    disabled={isLoading || !acunetixConfig.apiKey}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Test Connection
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'proxy' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Proxy Configuration</h3>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="proxyEnabled"
                  checked={proxyConfig.enabled}
                  onChange={(e) => setProxyConfig({ ...proxyConfig, enabled: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="proxyEnabled" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Proxy
                </label>
              </div>

              {proxyConfig.enabled && (
                <>
                  <div>
                    <label htmlFor="proxyType" className="block text-sm font-medium text-gray-700 mb-1">
                      Proxy Type
                    </label>
                    <select
                      id="proxyType"
                      value={proxyConfig.type}
                      onChange={(e) => setProxyConfig({ ...proxyConfig, type: e.target.value })}
                      className="select-styled w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="http">HTTP</option>
                      <option value="socks5">SOCKS5</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="proxyHost" className="block text-sm font-medium text-gray-700 mb-1">
                        Host
                      </label>
                      <input
                        type="text"
                        id="proxyHost"
                        value={proxyConfig.host}
                        onChange={(e) => setProxyConfig({ ...proxyConfig, host: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="127.0.0.1"
                      />
                    </div>
                    <div>
                      <label htmlFor="proxyPort" className="block text-sm font-medium text-gray-700 mb-1">
                        Port
                      </label>
                      <input
                        type="number"
                        id="proxyPort"
                        value={proxyConfig.port}
                        onChange={(e) => setProxyConfig({ ...proxyConfig, port: parseInt(e.target.value) || 0 })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="8080"
                        min="1"
                        max="65535"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="proxyUsername" className="block text-sm font-medium text-gray-700 mb-1">
                        Username (Optional)
                      </label>
                      <input
                        type="text"
                        id="proxyUsername"
                        value={proxyConfig.username}
                        onChange={(e) => setProxyConfig({ ...proxyConfig, username: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Username"
                      />
                    </div>
                    <div>
                      <label htmlFor="proxyPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Password (Optional)
                      </label>
                      <input
                        type="password"
                        id="proxyPassword"
                        value={proxyConfig.password}
                        onChange={(e) => setProxyConfig({ ...proxyConfig, password: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Password"
                      />
                    </div>
                  </div>
                </>
              )}

              <div className="border-t pt-4">
                <button
                  onClick={handleSaveProxyConfig}
                  disabled={isSaving}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      Save Proxy Configuration
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
