// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AbortScan(arg1) {
  return window['go']['main']['App']['AbortScan'](arg1);
}

export function ConfirmVulnerability(arg1) {
  return window['go']['main']['App']['ConfirmVulnerability'](arg1);
}

export function CreateScan(arg1) {
  return window['go']['main']['App']['CreateScan'](arg1);
}

export function CreateTarget(arg1) {
  return window['go']['main']['App']['CreateTarget'](arg1);
}

export function DeleteScan(arg1) {
  return window['go']['main']['App']['DeleteScan'](arg1);
}

export function DeleteTarget(arg1) {
  return window['go']['main']['App']['DeleteTarget'](arg1);
}

export function GetAcunetixConfig() {
  return window['go']['main']['App']['GetAcunetixConfig']();
}

export function GetAppConfig() {
  return window['go']['main']['App']['GetAppConfig']();
}

export function GetConfigPath() {
  return window['go']['main']['App']['GetConfigPath']();
}

export function GetProxyConfig() {
  return window['go']['main']['App']['GetProxyConfig']();
}

export function GetScanProfiles() {
  return window['go']['main']['App']['GetScanProfiles']();
}

export function GetScans(arg1, arg2) {
  return window['go']['main']['App']['GetScans'](arg1, arg2);
}

export function GetTargets(arg1, arg2) {
  return window['go']['main']['App']['GetTargets'](arg1, arg2);
}

export function GetVulnerabilities(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['GetVulnerabilities'](arg1, arg2, arg3, arg4);
}

export function GetVulnerability(arg1) {
  return window['go']['main']['App']['GetVulnerability'](arg1);
}

export function GetVulnerabilityStats() {
  return window['go']['main']['App']['GetVulnerabilityStats']();
}

export function MarkVulnerabilityFalsePositive(arg1) {
  return window['go']['main']['App']['MarkVulnerabilityFalsePositive'](arg1);
}

export function SetAcunetixConfig(arg1) {
  return window['go']['main']['App']['SetAcunetixConfig'](arg1);
}

export function SetProxyConfig(arg1) {
  return window['go']['main']['App']['SetProxyConfig'](arg1);
}

export function TestConnection() {
  return window['go']['main']['App']['TestConnection']();
}

export function TestProxy() {
  return window['go']['main']['App']['TestProxy']();
}
