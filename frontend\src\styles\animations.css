/* 优化动画和减少闪烁的CSS */

/* 全局优化 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 减少重绘和回流 */
.sidebar-transition {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 优化文本渲染 */
.text-smooth {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 页面切换动画 */
.page-transition {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 侧栏动画优化 */
.sidebar-item {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 侧栏平推动画 */
.sidebar-push {
  overflow: visible;
  position: relative;
}

.sidebar-content {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.sidebar-text {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity, transform;
}

.sidebar-collapsed .sidebar-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 按钮悬停优化 */
.button-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
}

.button-hover:hover {
  transform: translateY(-1px) translateZ(0);
}

/* 减少布局抖动 */
.layout-stable {
  contain: layout style paint;
}

/* 优化滚动性能 */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 统一的下拉框样式 */
.select-styled {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  background-color: #fff;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  min-height: 2.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.select-styled:hover {
  border-color: #9CA3AF;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.select-styled:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  outline: none;
}

.select-styled:disabled {
  background-color: #F9FAFB;
  color: #9CA3AF;
  cursor: not-allowed;
  opacity: 0.8;
}

/* 下拉列表选项样式 - 注意：原生select的option样式支持有限 */
.select-styled option {
  padding: 0.5rem 0.75rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 在支持的浏览器中应用样式 */
.select-styled option:checked {
  background-color: #3B82F6 !important;
  color: white !important;
  font-weight: 500;
}

.select-styled option:disabled {
  color: #9CA3AF !important;
  background-color: #F9FAFB !important;
}

/* 防止刷新闪烁 */
.content-stable {
  min-height: 200px;
  transition: opacity 0.2s ease-out;
}

.loading-overlay {
  position: relative;
}

.loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(2px);
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-overlay.loading::before {
  opacity: 1;
}

/* 刷新时的内容淡出效果 */
.refresh-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.refresh-transition.refreshing {
  opacity: 0.6;
  transform: translateY(2px);
}

/* 数据加载动画 */
.data-loading {
  animation: dataFadeIn 0.4s ease-out;
}

@keyframes dataFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表格行动画 */
.table-row {
  transition: all 0.2s ease-out;
}

.table-row:hover {
  background-color: #F9FAFB;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
