package main

import (
	"context"
	"fmt"
	"log"

	"Acunetix/acunetix"
	"Acunetix/config"
)

// App struct
type App struct {
	ctx            context.Context
	acunetixClient *acunetix.Client
	configManager  *config.ConfigManager
}

// NewApp creates a new App application struct
func NewApp() *App {
	configManager := config.NewConfigManager()

	// Load existing configuration or create default
	appConfig, err := configManager.LoadConfig()
	if err != nil {
		log.Printf("Failed to load config: %v", err)
	}

	app := &App{
		configManager: configManager,
	}

	// Initialize Acunetix client if configuration is available
	if appConfig.Acunetix.APIKey != "" {
		acunetixConfig := acunetix.Config{
			BaseURL:            appConfig.Acunetix.BaseURL,
			APIKey:             appConfig.Acunetix.APIKey,
			InsecureSkipVerify: appConfig.Acunetix.InsecureSkipVerify,
			Timeout:            appConfig.Acunetix.Timeout,
		}
		app.acunetixClient = acunetix.NewClient(acunetixConfig)
	}

	return app
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	log.Println("Acunetix Scanner Desktop App started")
}

// GetAppConfig returns the complete application configuration
func (a *App) GetAppConfig() *config.AppConfig {
	return a.configManager.GetConfig()
}

// GetAcunetixConfig returns the current Acunetix configuration
func (a *App) GetAcunetixConfig() config.AcunetixConfig {
	return a.configManager.GetConfig().Acunetix
}

// GetProxyConfig returns the current proxy configuration
func (a *App) GetProxyConfig() config.ProxyConfig {
	return a.configManager.GetConfig().Proxy
}

// SetAcunetixConfig updates the Acunetix configuration and creates a new client
func (a *App) SetAcunetixConfig(acunetixConfig config.AcunetixConfig) error {
	// Save configuration to file
	if err := a.configManager.UpdateAcunetixConfig(acunetixConfig); err != nil {
		return fmt.Errorf("failed to save Acunetix config: %w", err)
	}

	// Get current proxy configuration
	proxyConfig := a.configManager.GetConfig().Proxy

	// Create new Acunetix client with proxy support
	clientConfig := acunetix.Config{
		BaseURL:            acunetixConfig.BaseURL,
		APIKey:             acunetixConfig.APIKey,
		InsecureSkipVerify: acunetixConfig.InsecureSkipVerify,
		Timeout:            acunetixConfig.Timeout,
		Proxy: acunetix.ProxyConfig{
			Enabled:  proxyConfig.Enabled,
			Type:     proxyConfig.Type,
			Host:     proxyConfig.Host,
			Port:     proxyConfig.Port,
			Username: proxyConfig.Username,
			Password: proxyConfig.Password,
		},
	}
	a.acunetixClient = acunetix.NewClient(clientConfig)

	return nil
}

// SetProxyConfig updates the proxy configuration
func (a *App) SetProxyConfig(proxyConfig config.ProxyConfig) error {
	// Save proxy configuration to file
	if err := a.configManager.UpdateProxyConfig(proxyConfig); err != nil {
		return fmt.Errorf("failed to save proxy config: %w", err)
	}

	// Recreate Acunetix client with new proxy settings if client exists
	if a.acunetixClient != nil {
		acunetixConfig := a.configManager.GetConfig().Acunetix
		clientConfig := acunetix.Config{
			BaseURL:            acunetixConfig.BaseURL,
			APIKey:             acunetixConfig.APIKey,
			InsecureSkipVerify: acunetixConfig.InsecureSkipVerify,
			Timeout:            acunetixConfig.Timeout,
			Proxy: acunetix.ProxyConfig{
				Enabled:  proxyConfig.Enabled,
				Type:     proxyConfig.Type,
				Host:     proxyConfig.Host,
				Port:     proxyConfig.Port,
				Username: proxyConfig.Username,
				Password: proxyConfig.Password,
			},
		}
		a.acunetixClient = acunetix.NewClient(clientConfig)
	}

	return nil
}

// GetConfigPath returns the configuration file path
func (a *App) GetConfigPath() string {
	return a.configManager.GetConfigPath()
}

// TestProxy tests the proxy configuration
func (a *App) TestProxy() (*acunetix.ConnectionStatus, error) {
	if a.acunetixClient == nil {
		return &acunetix.ConnectionStatus{
			Connected: false,
			Message:   "Client not configured. Please set API configuration first.",
		}, nil
	}

	return a.acunetixClient.TestProxy()
}

// GetVulnerabilities retrieves vulnerabilities from Acunetix
func (a *App) GetVulnerabilities(cursor string, limit int, severity string, targetID string) (*acunetix.VulnerabilityListResponse, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetVulnerabilities(cursor, limit, severity, targetID)
}

// GetVulnerability retrieves a specific vulnerability by ID
func (a *App) GetVulnerability(vulnID string) (*acunetix.Vulnerability, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetVulnerability(vulnID)
}

// MarkVulnerabilityFalsePositive marks a vulnerability as false positive
func (a *App) MarkVulnerabilityFalsePositive(vulnID string) error {
	if a.acunetixClient == nil {
		return fmt.Errorf("client not configured")
	}

	return a.acunetixClient.MarkVulnerabilityFalsePositive(vulnID)
}

// ConfirmVulnerability confirms a vulnerability
func (a *App) ConfirmVulnerability(vulnID string) error {
	if a.acunetixClient == nil {
		return fmt.Errorf("client not configured")
	}

	return a.acunetixClient.ConfirmVulnerability(vulnID)
}

// GetVulnerabilityStats retrieves vulnerability statistics
func (a *App) GetVulnerabilityStats() (*acunetix.SeverityCounts, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetVulnerabilityStats()
}

// TestConnection tests the connection to Acunetix API
func (a *App) TestConnection() (*acunetix.ConnectionStatus, error) {
	if a.acunetixClient == nil {
		return &acunetix.ConnectionStatus{
			Connected: false,
			Message:   "Client not configured. Please set API configuration first.",
		}, nil
	}

	return a.acunetixClient.TestConnection()
}

// GetTargets retrieves targets from Acunetix
func (a *App) GetTargets(page int, limit int) (*acunetix.TargetListResponse, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetTargets(page, limit)
}

// CreateTarget creates a new target
func (a *App) CreateTarget(target acunetix.NewTarget) (*acunetix.Target, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.CreateTarget(target)
}

// DeleteTarget deletes a target
func (a *App) DeleteTarget(targetID string) error {
	if a.acunetixClient == nil {
		return fmt.Errorf("client not configured")
	}

	return a.acunetixClient.DeleteTarget(targetID)
}

// GetScans retrieves scans from Acunetix
func (a *App) GetScans(page int, limit int) (*acunetix.ScanListResponse, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetScans(page, limit)
}

// CreateScan creates a new scan
func (a *App) CreateScan(scan acunetix.NewScan) (*acunetix.Scan, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.CreateScan(scan)
}

// DeleteScan deletes a scan
func (a *App) DeleteScan(scanID string) error {
	if a.acunetixClient == nil {
		return fmt.Errorf("client not configured")
	}

	return a.acunetixClient.DeleteScan(scanID)
}

// AbortScan aborts a running scan
func (a *App) AbortScan(scanID string) error {
	if a.acunetixClient == nil {
		return fmt.Errorf("client not configured")
	}

	return a.acunetixClient.AbortScan(scanID)
}

// GetScanProfiles retrieves available scan profiles
func (a *App) GetScanProfiles() (*acunetix.ScanProfileListResponse, error) {
	if a.acunetixClient == nil {
		return nil, fmt.Errorf("client not configured")
	}

	return a.acunetixClient.GetScanProfiles()
}
