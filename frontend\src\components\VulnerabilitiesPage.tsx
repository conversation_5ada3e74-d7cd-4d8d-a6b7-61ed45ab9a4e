import React, { useState, useEffect } from 'react';
import {
  Shield,
  Search,
  RefreshCw,
  AlertTriangle,
  AlertCircle,
  Info,
  ExternalLink,
  Filter,
  Loader2,
  Check,
  X
} from 'lucide-react';
import { Vulnerability } from '../types/acunetix';
import {
  GetVulnerabilities,
  MarkVulnerabilityFalsePositive,
  ConfirmVulnerability
} from '../../wailsjs/go/main/App';
import CursorPagination from './CursorPagination';

const VulnerabilitiesPage: React.FC = () => {
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [cursors, setCursors] = useState<(string | null)[]>([null]); // 第一页的cursor是null
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);
  const [maxDiscoveredPage, setMaxDiscoveredPage] = useState(1); // 已发现的最大页数

  const getSeverityName = (severity: number) => {
    switch (severity) {
      case 4: return 'critical';
      case 3: return 'high';
      case 2: return 'medium';
      case 1: return 'low';
      case 0: return 'info';
      default: return 'unknown';
    }
  };

  // Load vulnerabilities on component mount and when filters change
  useEffect(() => {
    setCurrentPage(1);
    setCurrentCursor(null);
    setCursors([null]);
    setMaxDiscoveredPage(1);
    loadVulnerabilities(null);
  }, [severityFilter, pageSize]);

  const loadVulnerabilities = async (cursor: string | null = currentCursor) => {
    setLoading(true);
    try {
      console.log('Loading vulnerabilities with cursor:', cursor, 'pageSize:', pageSize, 'filter:', severityFilter);

      // Load vulnerabilities
      const response = await GetVulnerabilities(cursor || '', pageSize, severityFilter, '');
      console.log('Vulnerabilities response:', response);

      if (response && response.vulnerabilities) {
        console.log('Setting vulnerabilities:', response.vulnerabilities.length, 'items');
        setVulnerabilities(response.vulnerabilities);

        // Update pagination info
        setTotalCount(response.pagination.count);

        // Update cursors from API response - 动态扩展cursors数组
        const pagination = response.pagination as any;
        console.log('Full pagination response:', pagination);

        if (pagination.cursors && Array.isArray(pagination.cursors)) {
          console.log('API returned cursors:', pagination.cursors);

          // 动态扩展cursors数组
          console.log(`Processing cursors for page ${currentPage}:`, pagination.cursors);

          setCursors(prevCursors => {
            const newCursors = [...prevCursors];
            console.log('Previous global cursors:', prevCursors);

            // 根据当前页确定cursors在全局数组中的位置
            if (currentPage === 1) {
              // 第一页：API返回 [null, cursor2, cursor3]
              // 全局cursors应该是 [null, cursor2, cursor3]
              console.log('Processing first page cursors');
              pagination.cursors.forEach((cursor: string | null, index: number) => {
                newCursors[index] = cursor;
                console.log(`Set cursors[${index}] = ${cursor}`);
              });
            } else {
              // 其他页：API返回当前页及后续页的cursors
              // 例如第3页返回 [cursor3, cursor4, cursor5]
              // 需要将这些cursors放到正确的位置
              console.log(`Processing page ${currentPage} cursors`);
              pagination.cursors.forEach((cursor: string | null, index: number) => {
                const globalIndex = currentPage - 1 + index;
                newCursors[globalIndex] = cursor;
                console.log(`Set cursors[${globalIndex}] = ${cursor} (from API index ${index})`);
              });
            }

            console.log('Updated global cursors:', newCursors);

            // 同时更新maxDiscoveredPage
            const newMaxPage = newCursors.length;
            console.log(`Updating maxDiscoveredPage to ${newMaxPage}`);
            setMaxDiscoveredPage(newMaxPage);

            return newCursors;
          });
        } else {
          console.log('No cursors in API response');
        }

        // Calculate severity counts from actual data (for current page)
        const counts = response.vulnerabilities.reduce((acc, vuln) => {
          const severityName = getSeverityName(vuln.severity);
          acc[severityName] = (acc[severityName] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        setSeverityCounts({
          critical: counts.critical || 0,
          high: counts.high || 0,
          medium: counts.medium || 0,
          low: counts.low || 0,
          info: counts.info || 0
        });
      } else {
        console.log('No vulnerabilities in response');
        setVulnerabilities([]);
        setSeverityCounts({
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0
        });
      }
    } catch (error) {
      console.error('Failed to load vulnerabilities:', error);
      setVulnerabilities([]);
      setSeverityCounts({
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        info: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMarkFalsePositive = async (vulnId: string) => {
    try {
      await MarkVulnerabilityFalsePositive(vulnId);
      // Reload vulnerabilities to get updated status
      await loadVulnerabilities(currentCursor);
    } catch (error) {
      console.error('Failed to mark vulnerability as false positive:', error);
    }
  };

  const handleConfirmVulnerability = async (vulnId: string) => {
    try {
      await ConfirmVulnerability(vulnId);
      // Reload vulnerabilities to get updated status
      await loadVulnerabilities(currentCursor);
    } catch (error) {
      console.error('Failed to confirm vulnerability:', error);
    }
  };

  const handlePageChange = (page: number, cursor: string | null) => {
    console.log('Page change requested:', page, 'cursor:', cursor);
    setCurrentPage(page);
    setCurrentCursor(cursor);
    loadVulnerabilities(cursor);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    setCurrentCursor(null);
    setCursors([null]);
    setMaxDiscoveredPage(1);
    // loadVulnerabilities will be called by useEffect when pageSize changes
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'medium':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'low':
        return <AlertCircle className="w-4 h-4 text-blue-600" />;
      case 'info':
        return <Info className="w-4 h-4 text-gray-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'info':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const [severityCounts, setSeverityCounts] = useState({
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    info: 0
  });

  const filteredVulnerabilities = vulnerabilities.filter(vuln => {
    const matchesSearch = vuln.vt_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vuln.affects_url.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vuln.target_description.toLowerCase().includes(searchTerm.toLowerCase());

    const vulnSeverityName = getSeverityName(vuln.severity);
    const matchesSeverity = severityFilter === 'all' || vulnSeverityName === severityFilter;

    return matchesSearch && matchesSeverity;
  });

  return (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
        {[
          { severity: 'critical', count: severityCounts.critical || 0, color: 'bg-red-100 text-red-800' },
          { severity: 'high', count: severityCounts.high || 0, color: 'bg-orange-100 text-orange-800' },
          { severity: 'medium', count: severityCounts.medium || 0, color: 'bg-yellow-100 text-yellow-800' },
          { severity: 'low', count: severityCounts.low || 0, color: 'bg-blue-100 text-blue-800' },
          { severity: 'info', count: severityCounts.info || 0, color: 'bg-gray-100 text-gray-800' }
        ].map(({ severity, count, color }) => (
          <div key={severity} className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 capitalize">{severity}</p>
                <p className="text-lg font-bold text-gray-900">{count}</p>
              </div>
              <div className={`p-1.5 rounded-lg ${color}`}>
                {getSeverityIcon(severity)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search vulnerabilities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="select-styled px-3 py-1.5 text-sm border border-gray-300 rounded-md"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
              <option value="info">Info</option>
            </select>
          </div>

          <button
            onClick={() => loadVulnerabilities(currentCursor)}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center gap-1.5"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Vulnerabilities Table */}
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden content-stable ${loading ? 'loading-overlay loading' : ''}`}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            <span className="ml-2 text-sm text-gray-600">Loading vulnerabilities...</span>
          </div>
        ) : filteredVulnerabilities.length === 0 ? (
          <div className="text-center py-8">
            <Shield className="w-10 h-10 text-gray-400 mx-auto mb-3" />
            <h3 className="text-base font-medium text-gray-900 mb-2">No vulnerabilities found</h3>
            <p className="text-sm text-gray-600">
              {searchTerm || severityFilter !== 'all'
                ? 'No vulnerabilities match your search criteria.'
                : 'No vulnerabilities detected. Your targets appear to be secure!'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vulnerability</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severity</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Affects</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classification</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className={`bg-white divide-y divide-gray-200 refresh-transition ${loading ? 'refreshing' : ''} data-loading`}>
                {filteredVulnerabilities.map((vuln) => (
                  <tr key={vuln.vuln_id} className="table-row">
                    {/* Vulnerability */}
                    <td className="px-3 py-2">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs" title={vuln.vt_name}>
                        {vuln.vt_name}
                      </div>
                      <div className="text-xs text-gray-500 truncate max-w-xs" title={vuln.vuln_id}>
                        ID: {vuln.vuln_id}
                      </div>
                    </td>

                    {/* Severity */}
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getSeverityColor(getSeverityName(vuln.severity))}`}>
                        {getSeverityName(vuln.severity).toUpperCase()}
                      </span>
                      <div className="text-xs text-gray-500 mt-1">
                        {vuln.confidence}% confidence
                      </div>
                    </td>

                    {/* Target */}
                    <td className="px-3 py-2">
                      <div className="text-sm text-gray-900 truncate max-w-xs" title={vuln.target_id}>
                        Target ID: {vuln.target_id.substring(0, 8)}...
                      </div>
                      {vuln.target_description && (
                        <div className="text-xs text-gray-500 truncate max-w-xs" title={vuln.target_description}>
                          {vuln.target_description}
                        </div>
                      )}
                    </td>

                    {/* Affects */}
                    <td className="px-3 py-2">
                      <div className="text-sm text-gray-900 truncate max-w-xs" title={vuln.affects_url}>
                        {vuln.affects_url}
                      </div>
                      {vuln.affects_detail && (
                        <div className="text-xs text-gray-500 truncate max-w-xs" title={vuln.affects_detail}>
                          {vuln.affects_detail}
                        </div>
                      )}
                    </td>

                    {/* Classification */}
                    <td className="px-3 py-2">
                      <div className="flex flex-wrap gap-1">
                        {vuln.tags.map((tag, index) => {
                          if (tag.startsWith('CWE-')) {
                            return (
                              <span key={index} className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                {tag}
                              </span>
                            );
                          }
                          if (tag.startsWith('CVE-')) {
                            return (
                              <span key={index} className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                {tag}
                              </span>
                            );
                          }
                          return null;
                        })}
                        {vuln.tags.filter(tag => tag.startsWith('CWE-') || tag.startsWith('CVE-')).length === 0 && (
                          <span className="text-xs text-gray-500">No classification</span>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-3 py-2 whitespace-nowrap">
                      <div className="flex items-center gap-1">
                        <button className="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50" title="View details">
                          <ExternalLink className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Cursor Pagination */}
      <CursorPagination
        currentPage={currentPage}
        cursors={cursors}
        maxDiscoveredPage={maxDiscoveredPage}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        loading={loading}
        itemName="vulnerabilities"
        totalCount={totalCount}
        currentPageItemCount={vulnerabilities.length}
      />
    </div>
  );
};

export default VulnerabilitiesPage;
