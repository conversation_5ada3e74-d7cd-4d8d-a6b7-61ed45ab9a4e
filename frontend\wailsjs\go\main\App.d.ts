// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {acunetix} from '../models';
import {config} from '../models';

export function AbortScan(arg1:string):Promise<void>;

export function ConfirmVulnerability(arg1:string):Promise<void>;

export function CreateScan(arg1:acunetix.NewScan):Promise<acunetix.Scan>;

export function CreateTarget(arg1:acunetix.NewTarget):Promise<acunetix.Target>;

export function DeleteScan(arg1:string):Promise<void>;

export function DeleteTarget(arg1:string):Promise<void>;

export function GetAcunetixConfig():Promise<config.AcunetixConfig>;

export function GetAppConfig():Promise<config.AppConfig>;

export function GetConfigPath():Promise<string>;

export function GetProxyConfig():Promise<config.ProxyConfig>;

export function GetScanProfiles():Promise<acunetix.ScanProfileListResponse>;

export function GetScans(arg1:number,arg2:number):Promise<acunetix.ScanListResponse>;

export function GetTargets(arg1:number,arg2:number):Promise<acunetix.TargetListResponse>;

export function GetVulnerabilities(arg1:string,arg2:number,arg3:string,arg4:string):Promise<acunetix.VulnerabilityListResponse>;

export function GetVulnerability(arg1:string):Promise<acunetix.Vulnerability>;

export function GetVulnerabilityStats():Promise<acunetix.SeverityCounts>;

export function MarkVulnerabilityFalsePositive(arg1:string):Promise<void>;

export function SetAcunetixConfig(arg1:config.AcunetixConfig):Promise<void>;

export function SetProxyConfig(arg1:config.ProxyConfig):Promise<void>;

export function TestConnection():Promise<acunetix.ConnectionStatus>;

export function TestProxy():Promise<acunetix.ConnectionStatus>;
