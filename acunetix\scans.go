package acunetix

import (
	"fmt"
)

// ScanSession represents a scan session with complete API fields
type ScanSession struct {
	ScanSessionID  string         `json:"scan_session_id"`
	Status         string         `json:"status"`
	StartDate      string         `json:"start_date"`
	EndDate        string         `json:"end_date,omitempty"`
	Progress       int            `json:"progress"`
	ThreatScore    int            `json:"threat_score"`
	Threat         int            `json:"threat"`
	EventLevel     int            `json:"event_level"`
	SeverityCounts SeverityCounts `json:"severity_counts"`
	AcuSensor      bool           `json:"acusensor"`
}

// Scan represents a security scan with complete API fields
type Scan struct {
	// Basic scan information
	ScanID      string `json:"scan_id"`
	TargetID    string `json:"target_id"`
	ProfileID   string `json:"profile_id"`
	ProfileName string `json:"profile_name"`

	// Scan configuration
	UISessionID      string   `json:"ui_session_id"`
	ReportTemplateID []string `json:"report_template_id"`
	MaxScanTime      int      `json:"max_scan_time"`
	Incremental      bool     `json:"incremental"`

	// Scan status and timing
	Status    string   `json:"status"`
	StartDate string   `json:"start_date,omitempty"`
	NextRun   string   `json:"next_run,omitempty"`
	Schedule  Schedule `json:"schedule"`

	// Session information
	CurrentSession  ScanSession `json:"current_session"`
	PreviousSession ScanSession `json:"previous_session,omitempty"`

	// Target information (from ScanItemResponse)
	Target             Target `json:"target,omitempty"`
	Criticality        int    `json:"criticality"`
	ManualIntervention bool   `json:"manual_intervention"`
}

// Schedule represents scan scheduling information
type Schedule struct {
	Disable       bool   `json:"disable"`
	StartDate     string `json:"start_date"`
	TimeSensitive bool   `json:"time_sensitive"`
}

// NewScan represents a new scan to be created
type NewScan struct {
	TargetID    string   `json:"target_id"`
	ProfileID   string   `json:"profile_id"`
	Schedule    Schedule `json:"schedule"`
	UISessionID string   `json:"ui_session_id,omitempty"`
	ReportIDs   []string `json:"report_template_id,omitempty"`
}

// ScanListResponse represents the response for listing scans
type ScanListResponse struct {
	Scans      []Scan     `json:"scans"`
	Pagination Pagination `json:"pagination"`
}

// ScanCheck represents a scan check
type ScanCheck struct {
	CheckID string `json:"check_id"`
	Enabled bool   `json:"enabled"`
}

// ScanProfile represents a scan profile
type ScanProfile struct {
	ProfileID string      `json:"profile_id"`
	Name      string      `json:"name"`
	Custom    bool        `json:"custom"`
	Checks    []ScanCheck `json:"checks"`
}

// ScanProfileListResponse represents the response for listing scan profiles
type ScanProfileListResponse struct {
	ScanProfiles []ScanProfile `json:"scanning_profiles"`
}

// GetScans retrieves a list of scans with page-based pagination
func (c *Client) GetScans(page int, limit int) (*ScanListResponse, error) {
	req := c.client.R().SetResult(&ScanListResponse{})

	// Calculate cursor for page-based pagination
	if page > 1 {
		offset := (page - 1) * limit
		cursor := fmt.Sprintf("page_%d_offset_%d", page, offset)
		req.SetQueryParam("c", cursor)
	}
	if limit > 0 {
		req.SetQueryParam("l", fmt.Sprintf("%d", limit))
	}

	resp, err := req.Get("/scans")
	if err != nil {
		return nil, fmt.Errorf("failed to get scans: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*ScanListResponse), nil
}

// GetScan retrieves a specific scan by ID
func (c *Client) GetScan(scanID string) (*Scan, error) {
	resp, err := c.client.R().
		SetResult(&Scan{}).
		Get(fmt.Sprintf("/scans/%s", scanID))

	if err != nil {
		return nil, fmt.Errorf("failed to get scan: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*Scan), nil
}

// CreateScan creates a new scan
func (c *Client) CreateScan(newScan NewScan) (*Scan, error) {
	resp, err := c.client.R().
		SetBody(newScan).
		SetResult(&Scan{}).
		Post("/scans")

	if err != nil {
		return nil, fmt.Errorf("failed to create scan: %w", err)
	}

	if resp.StatusCode() != 201 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*Scan), nil
}

// DeleteScan deletes a scan
func (c *Client) DeleteScan(scanID string) error {
	resp, err := c.client.R().
		Delete(fmt.Sprintf("/scans/%s", scanID))

	if err != nil {
		return fmt.Errorf("failed to delete scan: %w", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// AbortScan aborts a running scan
func (c *Client) AbortScan(scanID string) error {
	resp, err := c.client.R().
		Post(fmt.Sprintf("/scans/%s/abort", scanID))

	if err != nil {
		return fmt.Errorf("failed to abort scan: %w", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// GetScanProfiles retrieves available scan profiles
func (c *Client) GetScanProfiles() (*ScanProfileListResponse, error) {
	resp, err := c.client.R().
		SetResult(&ScanProfileListResponse{}).
		Get("/scanning_profiles")

	if err != nil {
		return nil, fmt.Errorf("failed to get scan profiles: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*ScanProfileListResponse), nil
}
