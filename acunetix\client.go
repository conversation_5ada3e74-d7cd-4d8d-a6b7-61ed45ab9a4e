package acunetix

import (
	"crypto/tls"
	"fmt"
	"net/url"
	"time"

	"github.com/go-resty/resty/v2"
)

// Client represents the Acunetix API client
type Client struct {
	BaseURL string
	APIKey  string
	client  *resty.Client
}

// Config holds the configuration for the Acunetix client
type Config struct {
	BaseURL            string      `json:"baseUrl"`
	APIKey             string      `json:"apiKey"`
	InsecureSkipVerify bool        `json:"insecureSkipVerify"`
	Timeout            int         `json:"timeout"` // in seconds
	Proxy              ProxyConfig `json:"proxy"`
}

// ProxyConfig holds proxy configuration
type ProxyConfig struct {
	Enabled  bool   `json:"enabled"`
	Type     string `json:"type"` // "http", "socks5"
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// NewClient creates a new Acunetix API client
func NewClient(config Config) *Client {
	client := resty.New()

	// Set timeout (default 30 seconds)
	timeout := 30
	if config.Timeout > 0 {
		timeout = config.Timeout
	}
	client.SetTimeout(time.Duration(timeout) * time.Second)

	// Configure proxy if enabled
	if config.Proxy.Enabled && config.Proxy.Host != "" && config.Proxy.Port > 0 {
		var proxyURL string

		// Validate proxy type
		proxyType := config.Proxy.Type
		if proxyType != "http" && proxyType != "socks5" {
			proxyType = "http" // Default to HTTP if invalid type
		}

		if config.Proxy.Username != "" && config.Proxy.Password != "" {
			proxyURL = fmt.Sprintf("%s://%s:%s@%s:%d",
				proxyType,
				url.QueryEscape(config.Proxy.Username),
				url.QueryEscape(config.Proxy.Password),
				config.Proxy.Host,
				config.Proxy.Port)
		} else {
			proxyURL = fmt.Sprintf("%s://%s:%d",
				proxyType,
				config.Proxy.Host,
				config.Proxy.Port)
		}

		// Set proxy with error handling
		if err := client.SetProxy(proxyURL); err != nil {
			// Log proxy error but continue without proxy
			fmt.Printf("Warning: Failed to set proxy %s: %v\n", proxyURL, err)
		}
	}

	// Configure TLS
	client.SetTLSClientConfig(&tls.Config{
		InsecureSkipVerify: config.InsecureSkipVerify,
	})

	// Set default headers
	client.SetHeaders(map[string]string{
		"Content-Type": "application/json",
		"Accept":       "application/json",
		"X-Auth":       config.APIKey,
	})

	// Set base URL
	if config.BaseURL != "" {
		client.SetBaseURL(config.BaseURL)
	}

	return &Client{
		BaseURL: config.BaseURL,
		APIKey:  config.APIKey,
		client:  client,
	}
}

// TestConnection tests the connection to the Acunetix API
func (c *Client) TestConnection() (*ConnectionStatus, error) {
	// Create a temporary client with shorter timeout for testing
	testClient := c.client.Clone()
	testClient.SetTimeout(10 * time.Second)

	resp, err := testClient.R().
		SetResult(&ConnectionStatus{}).
		Get("/info")

	if err != nil {
		// Provide more detailed error information
		return &ConnectionStatus{
			Connected: false,
			Message:   fmt.Sprintf("Connection failed: %v", err),
		}, nil
	}

	if resp.StatusCode() != 200 {
		return &ConnectionStatus{
			Connected: false,
			Message:   fmt.Sprintf("HTTP %d: %s", resp.StatusCode(), resp.String()),
		}, nil
	}

	result := resp.Result().(*ConnectionStatus)
	if result == nil {
		result = &ConnectionStatus{}
	}
	result.Connected = true
	result.Message = "Connection successful"

	return result, nil
}

// TestProxy tests if the proxy configuration is working
func (c *Client) TestProxy() (*ConnectionStatus, error) {
	// Create a temporary client with shorter timeout for proxy testing
	testClient := c.client.Clone()
	testClient.SetTimeout(5 * time.Second)

	// Try to make a simple HTTP request through the proxy
	resp, err := testClient.R().
		Get("http://httpbin.org/ip")

	if err != nil {
		return &ConnectionStatus{
			Connected: false,
			Message:   fmt.Sprintf("Proxy test failed: %v", err),
		}, nil
	}

	if resp.StatusCode() != 200 {
		return &ConnectionStatus{
			Connected: false,
			Message:   fmt.Sprintf("Proxy test failed: HTTP %d", resp.StatusCode()),
		}, nil
	}

	return &ConnectionStatus{
		Connected: true,
		Message:   "Proxy connection successful",
	}, nil
}

// ConnectionStatus represents the status of the API connection
type ConnectionStatus struct {
	Connected bool   `json:"connected"`
	Message   string `json:"message"`
	Version   string `json:"version,omitempty"`
	Build     string `json:"build,omitempty"`
}

// ErrorResponse represents an API error response
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// APIResponse represents a generic API response wrapper
type APIResponse[T any] struct {
	Data    T              `json:"data,omitempty"`
	Error   *ErrorResponse `json:"error,omitempty"`
	Success bool           `json:"success"`
}
