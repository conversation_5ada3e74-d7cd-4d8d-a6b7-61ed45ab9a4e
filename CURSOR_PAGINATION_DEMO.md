# Vulnerabilities游标分页优化演示

## 🎯 **优化目标**
- 取消页面直接跳转，使用游标分页优化性能
- 根据API响应中的cursors数组动态显示页面选择器
- 当出现第X页时，页面选择器最多出现X页

## 📊 **API响应分析**

### **第一页请求**
```
GET /vulnerabilities?l=20
```

**响应中的cursors数组：**
```json
{
  "cursors": [
    null,                                                                    // 第一页
    "WzEwLCAzLCAiRGF0YWJhc2UgVXNlciBIYXMgQWRtaW4gUHJpdmlsZWdlcyIsIDM3MDU2MjE0ODg4ODQyNTM4NzNd", // 第二页
    "WzEwLCAyLCAiQXBhY2hlIFNlcnZlci1TdGF0dXMgRGV0ZWN0ZWQiLCAzNzA1NjQ0ODAzNDQzMTMxNjUyXQ=="     // 第三页
  ]
}
```

**页面选择器显示：** `[1] [2] [3]`

### **第二页请求**
```
GET /vulnerabilities?c=WzEwLCAzLCAiRGF0YWJhc2UgVXNlciBIYXMgQWRtaW4gUHJpdmlsZWdlcyIsIDM3MDU2MjE0ODg4ODQyNTM4NzNd&l=20
```

**响应中的cursors数组：**
```json
{
  "cursors": [
    "WzEwLCAzLCAiRGF0YWJhc2UgVXNlciBIYXMgQWRtaW4gUHJpdmlsZWdlcyIsIDM3MDU2MjE0ODg4ODQyNTM4NzNd", // 当前页（第二页）
    "WzEwLCAyLCAiQXBhY2hlIFNlcnZlci1TdGF0dXMgRGV0ZWN0ZWQiLCAzNzA1NjQ0ODAzNDQzMTMxNjUyXQ==",     // 第三页
    "WzEwLCAyLCAiU1NML1RMUyBOb3QgSW1wbGVtZW50ZWQiLCAzNzA1NjE4MDU2MzQ5NzQ2MjY4XQ=="             // 第四页
  ]
}
```

**页面选择器显示：** `[1] [2] [3] [4]`

## 🔧 **技术实现**

### **1. CursorPagination组件**

#### **核心特性：**
- ✅ **动态页码**：根据cursors数组长度显示页码按钮
- ✅ **游标导航**：每个页码按钮关联对应的cursor
- ✅ **性能优化**：避免计算偏移量，直接使用API提供的cursor
- ✅ **渐进式发现**：随着用户浏览，逐步发现更多页面

#### **关键代码：**
```typescript
interface CursorPaginationProps {
  currentPage: number;
  cursors: (string | null)[];  // API返回的cursors数组
  pageSize: number;
  onPageChange: (page: number, cursor: string | null) => void;
  // ...
}

// 计算总页数：cursors数组的长度就是当前已知的最大页数
const maxKnownPage = cursors.length;

// 生成页码按钮
const renderPageButtons = () => {
  const buttons = [];
  for (let i = 1; i <= maxKnownPage; i++) {
    buttons.push(
      <button
        onClick={() => onPageChange(i, cursors[i - 1])}
        className={currentPage === i ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}
      >
        {i}
      </button>
    );
  }
  return buttons;
};
```

### **2. VulnerabilitiesPage状态管理**

#### **状态变量：**
```typescript
const [currentPage, setCurrentPage] = useState(1);
const [cursors, setCursors] = useState<(string | null)[]>([null]); // 第一页的cursor是null
const [currentCursor, setCurrentCursor] = useState<string | null>(null);
```

#### **数据加载逻辑：**
```typescript
const loadVulnerabilities = async (cursor: string | null = currentCursor) => {
  const response = await GetVulnerabilities(cursor || '', pageSize, severityFilter, '');
  
  // 更新cursors数组
  const pagination = response.pagination as any;
  if (pagination.cursors && Array.isArray(pagination.cursors)) {
    setCursors(pagination.cursors);
  }
};
```

#### **页面切换处理：**
```typescript
const handlePageChange = (page: number, cursor: string | null) => {
  setCurrentPage(page);
  setCurrentCursor(cursor);
  loadVulnerabilities(cursor);
};
```

### **3. 后端API适配**

#### **恢复游标分页：**
```go
// GetVulnerabilities retrieves vulnerabilities from Acunetix API with cursor-based pagination
func (c *Client) GetVulnerabilities(cursor string, limit int, severity string, targetID string) (*VulnerabilityListResponse, error) {
    params := map[string]string{
        "l": fmt.Sprintf("%d", limit),
    }
    
    // Use cursor for pagination
    if cursor != "" {
        params["c"] = cursor
    }
    // ...
}
```

## 🎨 **用户体验优化**

### **页面选择器行为：**

1. **初始状态**：显示 `[1] [2] [3]`（基于第一页API响应）
2. **点击第2页**：显示 `[1] [2] [3] [4]`（基于第二页API响应）
3. **点击第3页**：可能显示 `[1] [2] [3] [4] [5]`（如果有更多页面）

### **导航限制：**
- ✅ **只能访问已知页面**：用户只能点击已发现的页码
- ✅ **渐进式发现**：通过逐页浏览发现更多页面
- ✅ **性能优化**：避免无效的页面跳转请求

### **视觉反馈：**
```
Page 2 of 4+ (more pages available)
```
- 显示当前页码和已知最大页数
- 提示可能有更多页面可用

## 📈 **性能优势**

### **相比传统分页：**
1. **无需计算偏移量**：直接使用API提供的cursor
2. **减少无效请求**：只能访问已知存在的页面
3. **数据库友好**：cursor分页对数据库更高效
4. **一致性保证**：避免数据变化导致的分页错位

### **内存优化：**
- 只缓存当前页面的cursors信息
- 不需要维护全局页面索引
- 状态管理更简单

## 🔄 **工作流程**

1. **初始加载**：请求第一页（cursor=null）
2. **获取cursors**：从API响应中提取cursors数组
3. **更新UI**：根据cursors数组长度显示页码按钮
4. **用户点击**：使用对应的cursor请求新页面
5. **更新cursors**：用新的cursors数组更新页码按钮
6. **循环发现**：随着浏览逐步发现更多页面

这种设计完美平衡了性能和用户体验，既保持了高效的数据加载，又提供了直观的分页导航！
