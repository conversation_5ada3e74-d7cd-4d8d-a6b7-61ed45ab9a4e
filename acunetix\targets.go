package acunetix

import (
	"fmt"
)

// SeverityCounts represents vulnerability severity counts
type SeverityCounts struct {
	Critical int `json:"critical"`
	High     int `json:"high"`
	Medium   int `json:"medium"`
	Low      int `json:"low"`
	Info     int `json:"info"`
}

// ScanAuthorization represents scan authorization information
type ScanAuthorization struct {
	URL     string `json:"url"`
	Content string `json:"content"`
}

// Link represents a quick-access link
type Link struct {
	Rel  string `json:"rel"`
	Href string `json:"href"`
}

// Target represents a scan target with complete API fields
type Target struct {
	// Basic target information
	TargetID    string `json:"target_id"`
	Address     string `json:"address"`
	Description string `json:"description"`
	Criticality int    `json:"criticality"`
	Type        string `json:"type"`

	// Scan information
	LastScanDate          string `json:"last_scan_date,omitempty"`
	LastScanID            string `json:"last_scan_id,omitempty"`
	LastScanSessionID     string `json:"last_scan_session_id,omitempty"`
	LastScanSessionStatus string `json:"last_scan_session_status,omitempty"`

	// Security information
	SeverityCounts SeverityCounts `json:"severity_counts"`
	Threat         int            `json:"threat"`
	ThreatScore    int            `json:"threat_score"`

	// Additional API fields
	ScanAuthorization  ScanAuthorization `json:"scan_authorization,omitempty"`
	ContinuousMode     bool              `json:"continuous_mode"`
	Links              []Link            `json:"links,omitempty"`
	ManualIntervention bool              `json:"manual_intervention"`
	Verification       string            `json:"verification,omitempty"`

	// FQDN related fields
	FQDN       string `json:"fqdn,omitempty"`
	FQDNHash   string `json:"fqdn_hash,omitempty"`
	FQDNStatus string `json:"fqdn_status,omitempty"`
	FQDNTMHash string `json:"fqdn_tm_hash,omitempty"`

	// System fields
	DeletedAt                string `json:"deleted_at,omitempty"`
	DefaultScanningProfileID string `json:"default_scanning_profile_id,omitempty"`
	DefaultOverrides         string `json:"default_overrides,omitempty"`

	// Legacy fields for compatibility
	AddedDate string `json:"added_date,omitempty"`
}

// NewTarget represents a new target to be created
type NewTarget struct {
	Address     string `json:"address"`
	Description string `json:"description"`
	Criticality int    `json:"criticality"`
	Type        string `json:"type"`
}

// TargetListResponse represents the response for listing targets
type TargetListResponse struct {
	Targets    []Target   `json:"targets"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination information
type Pagination struct {
	Count      int    `json:"count"`
	Cursor     string `json:"cursor_hash"`
	NextCursor string `json:"next_cursor"`
	Sort       string `json:"sort"`
}

// GetTargets retrieves a list of targets with page-based pagination
func (c *Client) GetTargets(page int, limit int) (*TargetListResponse, error) {
	req := c.client.R().SetResult(&TargetListResponse{})

	// Calculate cursor for page-based pagination
	if page > 1 {
		offset := (page - 1) * limit
		cursor := fmt.Sprintf("page_%d_offset_%d", page, offset)
		req.SetQueryParam("c", cursor)
	}
	if limit > 0 {
		req.SetQueryParam("l", fmt.Sprintf("%d", limit))
	}

	resp, err := req.Get("/targets")
	if err != nil {
		return nil, fmt.Errorf("failed to get targets: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*TargetListResponse), nil
}

// GetTarget retrieves a specific target by ID
func (c *Client) GetTarget(targetID string) (*Target, error) {
	resp, err := c.client.R().
		SetResult(&Target{}).
		Get(fmt.Sprintf("/targets/%s", targetID))

	if err != nil {
		return nil, fmt.Errorf("failed to get target: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*Target), nil
}

// CreateTarget creates a new target
func (c *Client) CreateTarget(newTarget NewTarget) (*Target, error) {
	resp, err := c.client.R().
		SetBody(newTarget).
		SetResult(&Target{}).
		Post("/targets")

	if err != nil {
		return nil, fmt.Errorf("failed to create target: %w", err)
	}

	if resp.StatusCode() != 201 {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return resp.Result().(*Target), nil
}

// DeleteTarget deletes a target
func (c *Client) DeleteTarget(targetID string) error {
	resp, err := c.client.R().
		Delete(fmt.Sprintf("/targets/%s", targetID))

	if err != nil {
		return fmt.Errorf("failed to delete target: %w", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("API error: %d - %s", resp.StatusCode(), resp.String())
	}

	return nil
}
