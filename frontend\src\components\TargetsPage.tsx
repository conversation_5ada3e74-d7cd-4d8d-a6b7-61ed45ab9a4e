import React, { useState, useEffect } from 'react';
import {
  Target as TargetIcon,
  Search,
  Trash2,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { Target, TargetListResponse, NewTarget } from '../types/acunetix';
import { GetTargets, CreateTarget, DeleteTarget } from '../../wailsjs/go/main/App';
import Pagination from './Pagination';

interface TargetsPageProps {
  showCreateModal: boolean;
  setShowCreateModal: (show: boolean) => void;
}

const TargetsPage: React.FC<TargetsPageProps> = ({ showCreateModal, setShowCreateModal }) => {
  const [targets, setTargets] = useState<Target[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [newTarget, setNewTarget] = useState<NewTarget>({
    address: '',
    description: '',
    criticality: 10,
    type: 'default'
  });
  const [creating, setCreating] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    setCurrentPage(1);
    loadTargets(1);
  }, [pageSize]);

  const loadTargets = async (page: number = currentPage) => {
    setLoading(true);
    try {
      const response = await GetTargets(page, pageSize);
      setTargets(response.targets || []);
      setTotalCount(response.pagination.count);
    } catch (error) {
      console.error('Failed to load targets:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadTargets(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    // loadTargets will be called by useEffect when pageSize changes
  };

  const handleCreateTarget = async () => {
    if (!newTarget.address.trim()) return;
    
    setCreating(true);
    try {
      const target = await CreateTarget(newTarget);
      setTargets(prev => [target, ...prev]);
      setShowCreateModal(false);
      setNewTarget({
        address: '',
        description: '',
        criticality: 10,
        type: 'default'
      });
    } catch (error) {
      console.error('Failed to create target:', error);
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteTarget = async (targetId: string) => {
    if (!confirm('Are you sure you want to delete this target?')) return;
    
    try {
      await DeleteTarget(targetId);
      setTargets(prev => prev.filter(t => t.target_id !== targetId));
    } catch (error) {
      console.error('Failed to delete target:', error);
    }
  };

  const filteredTargets = targets.filter(target =>
    target.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    target.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getThreatLevelColor = (threat: number) => {
    switch (threat) {
      case 4: return 'text-red-600 bg-red-100 border-red-200';
      case 3: return 'text-orange-600 bg-orange-100 border-orange-200';
      case 2: return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 1: return 'text-blue-600 bg-blue-100 border-blue-200';
      case 0: return 'text-gray-600 bg-gray-100 border-gray-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getThreatLevelLabel = (threat: number) => {
    switch (threat) {
      case 4: return 'Critical';
      case 3: return 'High';
      case 2: return 'Medium';
      case 1: return 'Low';
      case 0: return 'Informational';
      default: return 'Unknown';
    }
  };



  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  };

  const getLastScanStyle = (status: string | undefined, hasDate: boolean) => {
    if (!hasDate) {
      return 'px-2 py-1 text-xs rounded bg-gray-100 text-gray-700'; // null时间灰底
    }

    switch (status?.toLowerCase()) {
      case 'completed':
        return 'px-2 py-1 text-xs rounded bg-green-100 text-green-800'; // Completed绿底
      case 'processing':
      case 'scanning':
        return 'px-2 py-1 text-xs rounded bg-blue-100 text-blue-800'; // Processing蓝底
      default:
        return 'px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800'; // 其他状态黄底
    }
  };





  return (
    <div className="space-y-4">
      {/* Search and Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search targets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={() => loadTargets(currentPage)}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center gap-1.5"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Targets Table */}
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden content-stable ${loading ? 'loading-overlay loading' : ''}`}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            <span className="ml-2 text-sm text-gray-600">Loading targets...</span>
          </div>
        ) : filteredTargets.length === 0 ? (
          <div className="text-center py-8">
            <TargetIcon className="w-10 h-10 text-gray-400 mx-auto mb-3" />
            <h3 className="text-base font-medium text-gray-900 mb-2">No targets found</h3>
            <p className="text-sm text-gray-600 mb-3">
              {searchTerm ? 'No targets match your search criteria.' : 'Get started by adding your first target.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Add Target
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Threat</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vulnerabilities</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Scan</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className={`bg-white divide-y divide-gray-200 refresh-transition ${loading ? 'refreshing' : ''} data-loading`}>
                {filteredTargets.map((target) => (
                  <tr key={target.target_id} className="table-row">
                    {/* Target */}
                    <td className="px-3 py-2">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs" title={target.address}>
                        {target.address}
                      </div>
                      {target.description && (
                        <div className="text-xs text-gray-500 truncate max-w-xs" title={target.description}>
                          {target.description}
                        </div>
                      )}
                    </td>

                    {/* Threat */}
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded border ${getThreatLevelColor(target.threat)}`}>
                        {getThreatLevelLabel(target.threat)} ({target.threat})
                      </span>
                    </td>

                    {/* Vulnerabilities */}
                    <td className="px-3 py-2">
                      <div className="flex flex-wrap gap-1">
                        {target.severity_counts.critical > 0 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            {target.severity_counts.critical}C
                          </span>
                        )}
                        {target.severity_counts.high > 0 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                            {target.severity_counts.high}H
                          </span>
                        )}
                        {target.severity_counts.medium > 0 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                            {target.severity_counts.medium}M
                          </span>
                        )}
                        {target.severity_counts.low > 0 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {target.severity_counts.low}L
                          </span>
                        )}
                        {target.severity_counts.info > 0 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            {target.severity_counts.info}I
                          </span>
                        )}
                        {(target.severity_counts.critical + target.severity_counts.high + target.severity_counts.medium + target.severity_counts.low + target.severity_counts.info) === 0 && (
                          <span className="text-xs text-gray-500">No issues</span>
                        )}
                      </div>
                    </td>

                    {/* Last Scan (with Status integrated via background color) */}
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className={getLastScanStyle(target.last_scan_session_status, !!target.last_scan_date)}>
                        {target.last_scan_date ? formatDateTime(target.last_scan_date) : 'Never'}
                      </span>
                    </td>

                    {/* Actions */}
                    <td className="px-3 py-2 whitespace-nowrap">
                      <button
                        onClick={() => handleDeleteTarget(target.target_id)}
                        className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50"
                        title="Delete target"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        loading={loading}
        itemName="targets"
      />

      {/* Create Target Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={() => setShowCreateModal(false)} />
            
            <div className="inline-block w-full max-w-md p-4 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
              <h3 className="text-base font-medium text-gray-900 mb-3">Add New Target</h3>

              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Target URL/Address *
                  </label>
                  <input
                    type="text"
                    value={newTarget.address}
                    onChange={(e) => setNewTarget({ ...newTarget, address: e.target.value })}
                    placeholder="https://example.com"
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={newTarget.description}
                    onChange={(e) => setNewTarget({ ...newTarget, description: e.target.value })}
                    placeholder="Optional description"
                    rows={2}
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Criticality
                  </label>
                  <select
                    value={newTarget.criticality}
                    onChange={(e) => setNewTarget({ ...newTarget, criticality: parseInt(e.target.value) })}
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={0}>Low</option>
                    <option value={10}>Normal</option>
                    <option value={20}>High</option>
                    <option value={30}>Critical</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateTarget}
                  disabled={creating || !newTarget.address.trim()}
                  className="flex-1 bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-1.5"
                >
                  {creating ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Target'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TargetsPage;
