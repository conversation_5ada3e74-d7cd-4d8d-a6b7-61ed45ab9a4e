# 游标分页动态扩展测试场景

## 🎯 **优化目标验证**
- ✅ API返回始终有cursors
- ✅ 每当出现新的cursors则提供新的页面选择器
- ✅ 动态扩展页面选择器，不替换已有的cursors

## 📊 **测试场景详解**

### **场景1：访问第一页**
```
请求: GET /vulnerabilities?l=20
```

**API响应：**
```json
{
  "cursors": [
    null,                    // 第一页cursor
    "cursor_page_2",         // 第二页cursor
    "cursor_page_3"          // 第三页cursor
  ],
  "vulnerabilities": [...20 items...]
}
```

**前端处理逻辑：**
```typescript
// currentPage = 1
if (currentPage === 1) {
  // 第一页：直接使用API返回的cursors
  pagination.cursors.forEach((cursor, index) => {
    newCursors[index] = cursor;
  });
}

// 结果：
// newCursors = [null, "cursor_page_2", "cursor_page_3"]
// maxDiscoveredPage = 3
```

**页面选择器显示：** `[1] [2] [3]`

### **场景2：访问第三页**
```
请求: GET /vulnerabilities?c=cursor_page_3&l=20
```

**API响应：**
```json
{
  "cursors": [
    "cursor_page_3",         // 当前页（第三页）cursor
    "cursor_page_4",         // 第四页cursor（新发现）
    "cursor_page_5"          // 第五页cursor（新发现）
  ],
  "vulnerabilities": [...20 items...]
}
```

**前端处理逻辑：**
```typescript
// currentPage = 3
// prevCursors = [null, "cursor_page_2", "cursor_page_3"]

if (currentPage !== 1) {
  // 其他页：将API cursors放到正确的全局位置
  pagination.cursors.forEach((cursor, index) => {
    const globalIndex = currentPage - 1 + index; // 3-1+0=2, 3-1+1=3, 3-1+2=4
    newCursors[globalIndex] = cursor;
  });
}

// 结果：
// newCursors[2] = "cursor_page_3"  (第3页)
// newCursors[3] = "cursor_page_4"  (第4页，新发现)
// newCursors[4] = "cursor_page_5"  (第5页，新发现)
// 
// 最终：newCursors = [null, "cursor_page_2", "cursor_page_3", "cursor_page_4", "cursor_page_5"]
// maxDiscoveredPage = 5
```

**页面选择器显示：** `[1] [2] [3] [4] [5]`

### **场景3：访问第五页（最后一页）**
```
请求: GET /vulnerabilities?c=cursor_page_5&l=20
```

**API响应：**
```json
{
  "cursors": [
    "cursor_page_5"          // 当前页（第五页）cursor，没有新增
  ],
  "vulnerabilities": [...15 items...]  // 少于20项，说明是最后一页
}
```

**前端处理逻辑：**
```typescript
// currentPage = 5
// prevCursors = [null, "cursor_page_2", "cursor_page_3", "cursor_page_4", "cursor_page_5"]

pagination.cursors.forEach((cursor, index) => {
  const globalIndex = currentPage - 1 + index; // 5-1+0=4
  newCursors[globalIndex] = cursor;
});

// 结果：
// newCursors[4] = "cursor_page_5"  (第5页，确认)
// 
// 最终：newCursors = [null, "cursor_page_2", "cursor_page_3", "cursor_page_4", "cursor_page_5"]
// maxDiscoveredPage = 5 (没有变化)
```

**页面选择器显示：** `[1] [2] [3] [4] [5]` (没有变化)

## 🔧 **技术实现细节**

### **状态管理优化**
```typescript
const [cursors, setCursors] = useState<(string | null)[]>([null]);
const [maxDiscoveredPage, setMaxDiscoveredPage] = useState(1);
```

**关键特性：**
- ✅ **cursors数组**：维护全局的cursor映射
- ✅ **maxDiscoveredPage**：独立跟踪已发现的最大页数
- ✅ **动态扩展**：只添加新发现的cursors，不替换已有的

### **Cursors扩展算法**
```typescript
setCursors(prevCursors => {
  const newCursors = [...prevCursors];
  
  if (currentPage === 1) {
    // 第一页：直接映射
    pagination.cursors.forEach((cursor, index) => {
      newCursors[index] = cursor;
    });
  } else {
    // 其他页：计算全局位置
    pagination.cursors.forEach((cursor, index) => {
      const globalIndex = currentPage - 1 + index;
      newCursors[globalIndex] = cursor;
    });
  }
  
  setMaxDiscoveredPage(newCursors.length);
  return newCursors;
});
```

### **页面选择器生成**
```typescript
// 使用maxDiscoveredPage而不是cursors.length
const maxKnownPage = maxDiscoveredPage;

for (let i = 1; i <= maxKnownPage; i++) {
  const cursor = cursors[i - 1];
  // 生成页码按钮
}
```

## 📈 **性能优势**

### **内存效率**
- ✅ **增量扩展**：只添加新发现的cursors
- ✅ **避免重复**：不重新获取已知的cursors
- ✅ **状态保持**：用户浏览历史得到保留

### **网络优化**
- ✅ **减少请求**：已访问的页面可以直接跳转
- ✅ **智能发现**：通过浏览自然发现更多页面
- ✅ **缓存友好**：cursor具有确定性

### **用户体验**
- ✅ **渐进式发现**：页面选择器随着浏览逐步扩展
- ✅ **直接跳转**：可以跳转到任何已发现的页面
- ✅ **状态一致**：页面选择器状态在会话中保持

## 🎨 **UI行为验证**

### **页面选择器演进**
```
初始状态:     [1]
访问第1页后:   [1] [2] [3]
访问第3页后:   [1] [2] [3] [4] [5]
访问第5页后:   [1] [2] [3] [4] [5] (无变化)
```

### **调试信息输出**
```
Processing cursors for page 1: [null, "cursor2", "cursor3"]
Previous global cursors: [null]
Processing first page cursors
Set cursors[0] = null
Set cursors[1] = cursor2
Set cursors[2] = cursor3
Updated global cursors: [null, "cursor2", "cursor3"]
Updating maxDiscoveredPage to 3

Processing cursors for page 3: ["cursor3", "cursor4", "cursor5"]
Previous global cursors: [null, "cursor2", "cursor3"]
Processing page 3 cursors
Set cursors[2] = cursor3 (from API index 0)
Set cursors[3] = cursor4 (from API index 1)
Set cursors[4] = cursor5 (from API index 2)
Updated global cursors: [null, "cursor2", "cursor3", "cursor4", "cursor5"]
Updating maxDiscoveredPage to 5
```

## ✅ **验证清单**

- [ ] 第一页访问后显示正确的页码数量
- [ ] 跳转到中间页面后发现新的页码
- [ ] 访问最后一页时页码不再增加
- [ ] 可以直接跳转到任何已发现的页面
- [ ] 过滤器改变时正确重置状态
- [ ] 页面大小改变时正确重置状态
- [ ] 控制台输出详细的调试信息

这种实现完美满足了您的需求：API始终返回cursors，每当出现新的cursors就扩展页面选择器！
