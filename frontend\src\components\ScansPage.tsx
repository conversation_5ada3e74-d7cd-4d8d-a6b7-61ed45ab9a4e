import React, { useState, useEffect } from 'react';
import {
  Play,
  Pause,
  Square,
  Search,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Target as TargetIcon
} from 'lucide-react';
import { Scan } from '../types/acunetix';
import { GetScans, AbortScan } from '../../wailsjs/go/main/App';
import Pagination from './Pagination';

interface ScansPageProps {
  showCreateModal: boolean;
  setShowCreateModal: (show: boolean) => void;
}

const ScansPage: React.FC<ScansPageProps> = ({ showCreateModal, setShowCreateModal }) => {
  const [scans, setScans] = useState<Scan[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);

  // Mock data for demonstration
  useEffect(() => {
    setCurrentPage(1);
    loadScans(1);
  }, [pageSize]);

  const loadScans = async (page: number = currentPage) => {
    setLoading(true);
    try {
      const response = await GetScans(page, pageSize);

      if (response && response.scans) {
        setScans(response.scans);
        setTotalCount(response.pagination.count);
      } else {
        setScans([]);
      }
    } catch (error) {
      console.error('Failed to load scans:', error);
      setScans([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadScans(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    // loadScans will be called by useEffect when pageSize changes
  };

  const handleAbortScan = async (scanId: string) => {
    try {
      await AbortScan(scanId);
      // Reload scans to get updated status
      await loadScans(currentPage);
    } catch (error) {
      console.error('Failed to abort scan:', error);
    }
  };



  const calculateDuration = (startDate: string, endDate?: string) => {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000 / 60); // minutes

    if (duration < 60) {
      return `${duration}m`;
    }
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    return `${hours}h ${minutes}m`;
  };

  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  };

  const getScheduleStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'px-2 py-1 text-xs rounded bg-green-100 text-green-800';
      case 'processing':
      case 'scanning':
        return 'px-2 py-1 text-xs rounded bg-blue-100 text-blue-800';
      case 'failed':
      case 'aborted':
        return 'px-2 py-1 text-xs rounded bg-red-100 text-red-800';
      case 'queued':
        return 'px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800';
      case 'scheduled':
        return 'px-2 py-1 text-xs rounded bg-purple-100 text-purple-800';
      case 'none':
        return 'px-2 py-1 text-xs rounded bg-gray-100 text-gray-700';
      default:
        return 'px-2 py-1 text-xs rounded bg-gray-100 text-gray-700';
    }
  };

  const formatScheduleInfo = (scan: any) => {
    const currentSession = scan.current_session;
    const nextRun = scan.next_run;
    const schedule = scan.schedule;

    // 如果有上次运行记录
    if (currentSession?.start_date) {
      return {
        type: 'last_run',
        time: formatDateTime(currentSession.start_date),
        status: currentSession.status
      };
    }

    // 如果有下次运行计划
    if (nextRun) {
      return {
        type: 'next_run',
        time: formatDateTime(nextRun),
        status: 'scheduled'
      };
    }

    // 如果有计划开始时间
    if (schedule?.start_date) {
      return {
        type: 'start_date',
        time: formatDateTime(schedule.start_date),
        status: 'scheduled'
      };
    }

    return {
      type: 'none',
      time: 'No schedule',
      status: 'none'
    };
  };

  const filteredScans = scans.filter(scan =>
    (scan.target?.address || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    scan.profile_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    scan.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      {/* Search and Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search scans..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={() => loadScans(currentPage)}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-3 py-1.5 text-sm rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center gap-1.5"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Scans List */}
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 content-stable ${loading ? 'loading-overlay loading' : ''}`}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            <span className="ml-2 text-sm text-gray-600">Loading scans...</span>
          </div>
        ) : filteredScans.length === 0 ? (
          <div className="text-center py-8">
            <Play className="w-10 h-10 text-gray-400 mx-auto mb-3" />
            <h3 className="text-base font-medium text-gray-900 mb-2">No scans found</h3>
            <p className="text-sm text-gray-600 mb-3">
              {searchTerm ? 'No scans match your search criteria.' : 'Start your first security scan.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                New Scan
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vulnerabilities</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className={`bg-white divide-y divide-gray-200 refresh-transition ${loading ? 'refreshing' : ''} data-loading`}>
                {filteredScans.map((scan) => {
                  const scheduleInfo = formatScheduleInfo(scan);
                  return (
                    <tr key={scan.scan_id} className="table-row">
                      {/* Target (整合描述，参考targets的实现) */}
                      <td className="px-3 py-2">
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs" title={scan.target?.address || 'Unknown'}>
                          {scan.target?.address || 'Unknown'}
                        </div>
                        {scan.target?.description && (
                          <div className="text-xs text-gray-500 truncate max-w-xs" title={scan.target.description}>
                            {scan.target.description}
                          </div>
                        )}
                      </td>

                      {/* Profile */}
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                        {scan.profile_name}
                      </td>

                      {/* Vulnerabilities (参考targets中的实现) */}
                      <td className="px-3 py-2">
                        <div className="flex flex-wrap gap-1">
                          {scan.current_session?.severity_counts ? (
                            <>
                              {scan.current_session.severity_counts.critical > 0 && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  {scan.current_session.severity_counts.critical}C
                                </span>
                              )}
                              {scan.current_session.severity_counts.high > 0 && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                                  {scan.current_session.severity_counts.high}H
                                </span>
                              )}
                              {scan.current_session.severity_counts.medium > 0 && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  {scan.current_session.severity_counts.medium}M
                                </span>
                              )}
                              {scan.current_session.severity_counts.low > 0 && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                  {scan.current_session.severity_counts.low}L
                                </span>
                              )}
                              {scan.current_session.severity_counts.info > 0 && (
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                  {scan.current_session.severity_counts.info}I
                                </span>
                              )}
                              {(scan.current_session.severity_counts.critical +
                                scan.current_session.severity_counts.high +
                                scan.current_session.severity_counts.medium +
                                scan.current_session.severity_counts.low +
                                scan.current_session.severity_counts.info) === 0 && (
                                <span className="text-xs text-gray-500">No issues</span>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-gray-500">Not scanned</span>
                          )}
                        </div>
                      </td>

                      {/* Schedule (状态背景色直接应用到时间文字) */}
                      <td className="px-3 py-2 whitespace-nowrap">
                        <span className={getScheduleStyle(scheduleInfo.status)}>
                          {scheduleInfo.type === 'last_run' && 'Last: '}
                          {scheduleInfo.type === 'next_run' && 'Next: '}
                          {scheduleInfo.type === 'start_date' && 'Start: '}
                          {scheduleInfo.time}
                        </span>
                      </td>

                      {/* Actions */}
                      <td className="px-3 py-2 whitespace-nowrap">
                        <div className="flex items-center gap-1">
                          {(scan.current_session?.status === 'processing' || scan.current_session?.status === 'queued') && (
                            <button
                              onClick={() => handleAbortScan(scan.scan_id)}
                              className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50"
                              title="Abort scan"
                            >
                              <Square className="w-4 h-4" />
                            </button>
                          )}
                          {scan.current_session?.status === 'completed' && (
                            <button className="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50" title="View results">
                              <Play className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        loading={loading}
        itemName="scans"
      />
    </div>
  );
};

export default ScansPage;
