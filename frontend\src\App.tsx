import React, { useState, useMemo } from 'react';
import { Plus, Play, FileText } from 'lucide-react';
import Layout from './components/Layout';
import SettingsPage from './components/SettingsPage';
import TargetsPage from './components/TargetsPage';
import ScansPage from './components/ScansPage';
import VulnerabilitiesPage from './components/VulnerabilitiesPage';
import ReportsPage from './components/ReportsPage';

function App() {
  const [currentPage, setCurrentPage] = useState('targets');
  const [showCreateTargetModal, setShowCreateTargetModal] = useState(false);
  const [showCreateScanModal, setShowCreateScanModal] = useState(false);
  const [showCreateReportModal, setShowCreateReportModal] = useState(false);

  const headerAction = useMemo(() => {
    switch (currentPage) {
      case 'targets':
        return (
          <button
            onClick={() => setShowCreateTargetModal(true)}
            className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center gap-1.5 transition-colors duration-200"
          >
            <Plus className="w-4 h-4" />
            Add Target
          </button>
        );
      case 'scans':
        return (
          <button
            onClick={() => setShowCreateScanModal(true)}
            className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center gap-1.5 transition-colors duration-200"
          >
            <Play className="w-4 h-4" />
            New Scan
          </button>
        );
      case 'reports':
        return (
          <button
            onClick={() => setShowCreateReportModal(true)}
            className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center gap-1.5 transition-colors duration-200"
          >
            <Plus className="w-4 h-4" />
            Generate Report
          </button>
        );
      default:
        return null;
    }
  }, [currentPage]);

  const currentPageComponent = useMemo(() => {
    switch (currentPage) {
      case 'targets':
        return <TargetsPage showCreateModal={showCreateTargetModal} setShowCreateModal={setShowCreateTargetModal} />;
      case 'scans':
        return <ScansPage showCreateModal={showCreateScanModal} setShowCreateModal={setShowCreateScanModal} />;
      case 'vulnerabilities':
        return <VulnerabilitiesPage />;
      case 'reports':
        return <ReportsPage showCreateModal={showCreateReportModal} setShowCreateModal={setShowCreateReportModal} />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <TargetsPage showCreateModal={showCreateTargetModal} setShowCreateModal={setShowCreateTargetModal} />;
    }
  }, [currentPage, showCreateTargetModal, showCreateScanModal, showCreateReportModal]);

  return (
    <Layout currentPage={currentPage} onPageChange={setCurrentPage} headerAction={headerAction}>
      <div className="page-transition">
        {currentPageComponent}
      </div>
    </Layout>
  );
}

export default App;
