/* 统一的下拉框样式 */
.select-styled {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 32px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.select-styled:hover {
  border-color: #9CA3AF;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.select-styled:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.select-styled:disabled {
  background-color: #F9FAFB;
  color: #9CA3AF;
  cursor: not-allowed;
}

/* 下拉列表选项样式 - 注意：原生select的option样式支持有限 */
.select-styled option {
  padding: 8px 12px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
}

/* 在支持的浏览器中应用样式 */
.select-styled option:checked {
  background-color: #3B82F6 !important;
  color: white !important;
  font-weight: 500;
}

.select-styled option:disabled {
  color: #9CA3AF !important;
  background-color: #F9FAFB !important;
}

/* 增强下拉框的整体外观 */
.select-styled {
  background-color: white;
  border: 1px solid #D1D5DB;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
}import React, { useState, useCallback, useMemo } from 'react';
import {
  Settings,
  Target,
  Play,
  Shield,
  FileText,
  Menu,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
  headerAction?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children, currentPage, onPageChange, headerAction }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const menuItems = [
    { id: 'targets', label: 'Targets', icon: Target },
    { id: 'scans', label: 'Scans', icon: Play },
    { id: 'vulnerabilities', label: 'Vulnerabilities', icon: Shield },
    { id: 'reports', label: 'Reports', icon: FileText },
  ];

  const settingsItem = { id: 'settings', label: 'Settings', icon: Settings };

  const handlePageChange = useCallback((pageId: string) => {
    onPageChange(pageId);
  }, [onPageChange]);

  const toggleSidebarCollapse = useCallback(() => {
    setSidebarCollapsed(!sidebarCollapsed);
  }, [sidebarCollapsed]);

  return (
    <div className="flex h-screen bg-gray-50 layout-stable">
      {/* Sidebar */}
      <div className={`
        bg-white shadow-lg transition-all duration-500 ease-out flex flex-col sidebar-transition sidebar-push
        ${sidebarCollapsed ? 'w-16 sidebar-collapsed' : 'w-64'}
      `}>
        <div className="sidebar-content">
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div className="flex-1 overflow-hidden">
              {!sidebarCollapsed && (
                <h1 className="text-xl font-bold text-gray-900 text-smooth sidebar-text">
                  Acunetix Scanner
                </h1>
              )}
            </div>
            {/* Collapse toggle button */}
            <button
              onClick={toggleSidebarCollapse}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
              title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <div className="transition-transform duration-300 ease-out">
                {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              </div>
            </button>
          </div>

          {/* Main navigation */}
          <nav className="mt-4 px-4 flex-1">
            <ul className="space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;

                return (
                  <li key={item.id}>
                    <button
                      onClick={() => handlePageChange(item.id)}
                      className={`
                        w-full flex items-center text-sm font-medium rounded-md transition-all duration-300 ease-out
                        ${sidebarCollapsed ? 'px-3 py-2 justify-center' : 'px-3 py-2'}
                        ${isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                        }
                      `}
                      title={sidebarCollapsed ? item.label : undefined}
                    >
                      <Icon className="w-5 h-5 flex-shrink-0" />
                      {!sidebarCollapsed && (
                        <span className="sidebar-text ml-3">
                          {item.label}
                        </span>
                      )}
                    </button>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Settings and version info at bottom */}
          <div className="mt-auto px-4 pb-4">
            {/* Settings button */}
            <div className="border-t border-gray-200 pt-4 mb-4">
              <button
                onClick={() => handlePageChange(settingsItem.id)}
                className={`
                  w-full flex items-center text-sm font-medium rounded-md transition-all duration-300 ease-out
                  ${sidebarCollapsed ? 'px-3 py-2 justify-center' : 'px-3 py-2'}
                  ${currentPage === settingsItem.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }
                `}
                title={sidebarCollapsed ? settingsItem.label : undefined}
              >
                <Settings className="w-5 h-5 flex-shrink-0" />
                {!sidebarCollapsed && (
                  <span className="sidebar-text ml-3">
                    {settingsItem.label}
                  </span>
                )}
              </button>
            </div>

            {/* Version info */}
            {!sidebarCollapsed && (
              <div className="text-xs text-gray-500 text-center sidebar-text">
                Acunetix Desktop v1.0.0
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 h-16 px-6">
          <div className="flex items-center justify-between w-full h-full">
            <h1 className="text-lg font-semibold text-gray-900">
              {menuItems.find(item => item.id === currentPage)?.label ||
               (currentPage === 'settings' ? 'Settings' : 'Acunetix Scanner')}
            </h1>
            {headerAction && (
              <div className="flex items-center">
                {headerAction}
              </div>
            )}
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto p-6 will-change-scroll scroll-smooth">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
