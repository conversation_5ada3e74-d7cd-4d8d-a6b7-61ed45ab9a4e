package acunetix

import (
	"encoding/json"
	"fmt"
)

// VulnClassification represents vulnerability classification information
type VulnClassification struct {
	OWASP []string `json:"owasp"`
	CWE   []string `json:"cwe"`
}

// VulnTarget represents target information for a vulnerability
type VulnTarget struct {
	Address     string `json:"address"`
	Description string `json:"description"`
}

// VulnReference represents a vulnerability reference
type VulnReference struct {
	URL   string `json:"url"`
	Title string `json:"title"`
}

// Vulnerability represents a security vulnerability based on actual API response
type Vulnerability struct {
	// Basic vulnerability information
	VulnID     string `json:"vuln_id"`
	VTName     string `json:"vt_name"`
	Severity   int    `json:"severity"`
	Confidence int    `json:"confidence"`

	// Affected information
	AffectsURL    string `json:"affects_url"`
	AffectsDetail string `json:"affects_detail"`

	// Target information
	TargetID          string `json:"target_id"`
	TargetDescription string `json:"target_description"`

	// Status and metadata
	Status      string   `json:"status"`
	LastSeen    string   `json:"last_seen"`
	Tags        []string `json:"tags"`
	Criticality int      `json:"criticality"`

	// Additional API fields
	App              string `json:"app"`
	Archived         bool   `json:"archived"`
	Continuous       bool   `json:"continuous"`
	IssueID          string `json:"issue_id"`
	IssueTrackerID   string `json:"issue_tracker_id"`
	PluginInstanceID string `json:"plugin_instance_id"`
	VTID             string `json:"vt_id"`
	VTCreated        string `json:"vt_created"`
	VTUpdated        string `json:"vt_updated"`
	APIOperationID   string `json:"api_operation_id"`
	APIType          string `json:"api_type"`
}

// VulnerabilityListResponse represents the API response for vulnerability list
type VulnerabilityListResponse struct {
	Vulnerabilities []Vulnerability `json:"vulnerabilities"`
	Pagination      Pagination      `json:"pagination"`
}

// GetVulnerabilities retrieves vulnerabilities from Acunetix API with cursor-based pagination
func (c *Client) GetVulnerabilities(cursor string, limit int, severity string, targetID string) (*VulnerabilityListResponse, error) {
	// Build query parameters
	params := map[string]string{
		"l": fmt.Sprintf("%d", limit),
	}

	// Use cursor for pagination
	if cursor != "" {
		params["c"] = cursor
	}

	if severity != "" && severity != "all" {
		params["q"] = fmt.Sprintf("severity:%s", severity)
	}

	if targetID != "" {
		if params["q"] != "" {
			params["q"] += fmt.Sprintf(";target_id:%s", targetID)
		} else {
			params["q"] = fmt.Sprintf("target_id:%s", targetID)
		}
	}

	// Make API request
	resp, err := c.client.R().
		SetQueryParams(params).
		Get("/vulnerabilities")

	if err != nil {
		return nil, fmt.Errorf("failed to get vulnerabilities: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	var result VulnerabilityListResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse vulnerabilities response: %w", err)
	}

	return &result, nil
}

// GetVulnerability retrieves a specific vulnerability by ID
func (c *Client) GetVulnerability(vulnID string) (*Vulnerability, error) {
	resp, err := c.client.R().
		Get(fmt.Sprintf("/vulnerabilities/%s", vulnID))

	if err != nil {
		return nil, fmt.Errorf("failed to get vulnerability: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	var vuln Vulnerability
	if err := json.Unmarshal(resp.Body(), &vuln); err != nil {
		return nil, fmt.Errorf("failed to parse vulnerability response: %w", err)
	}

	return &vuln, nil
}

// MarkVulnerabilityFalsePositive marks a vulnerability as false positive
func (c *Client) MarkVulnerabilityFalsePositive(vulnID string) error {
	resp, err := c.client.R().
		SetBody(map[string]interface{}{
			"false_positive": true,
		}).
		Patch(fmt.Sprintf("/vulnerabilities/%s", vulnID))

	if err != nil {
		return fmt.Errorf("failed to mark vulnerability as false positive: %w", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// ConfirmVulnerability confirms a vulnerability
func (c *Client) ConfirmVulnerability(vulnID string) error {
	resp, err := c.client.R().
		SetBody(map[string]interface{}{
			"confirmed": true,
		}).
		Patch(fmt.Sprintf("/vulnerabilities/%s", vulnID))

	if err != nil {
		return fmt.Errorf("failed to confirm vulnerability: %w", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// GetVulnerabilityStats retrieves vulnerability statistics
func (c *Client) GetVulnerabilityStats() (*SeverityCounts, error) {
	resp, err := c.client.R().
		Get("/vulnerabilities/stats")

	if err != nil {
		return nil, fmt.Errorf("failed to get vulnerability stats: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	var stats SeverityCounts
	if err := json.Unmarshal(resp.Body(), &stats); err != nil {
		return nil, fmt.Errorf("failed to parse vulnerability stats response: %w", err)
	}

	return &stats, nil
}
