import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CursorPaginationProps {
  currentPage: number;
  cursors: (string | null)[];
  maxDiscoveredPage: number;
  pageSize: number;
  onPageChange: (page: number, cursor: string | null) => void;
  onPageSizeChange: (pageSize: number) => void;
  loading?: boolean;
  itemName?: string;
  totalCount?: number;
  currentPageItemCount?: number;
}

const CursorPagination: React.FC<CursorPaginationProps> = ({
  currentPage,
  cursors,
  maxDiscoveredPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  loading = false,
  itemName = 'items',
  totalCount,
  currentPageItemCount = 0
}) => {
  const pageSizeOptions = [10, 20, 50, 100];
  
  // 使用传入的maxDiscoveredPage作为已知的最大页数
  const maxKnownPage = maxDiscoveredPage;

  // 调试信息
  console.log('CursorPagination render:', {
    currentPage,
    cursors,
    maxDiscoveredPage,
    maxKnownPage,
    pageSize,
    currentPageItemCount,
    'cursors.length': cursors.length
  });

  // 确保maxKnownPage至少为1
  const actualMaxKnownPage = Math.max(1, maxKnownPage);
  console.log('actualMaxKnownPage:', actualMaxKnownPage);

  // 计算当前页显示的记录范围
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = (currentPage - 1) * pageSize + currentPageItemCount;

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons = [];

    console.log('Rendering page buttons, actualMaxKnownPage:', actualMaxKnownPage, 'cursors:', cursors);

    for (let i = 1; i <= actualMaxKnownPage; i++) {
      const cursor = cursors[i - 1];
      console.log(`Page ${i} cursor:`, cursor);

      buttons.push(
        <button
          key={i}
          onClick={() => {
            console.log(`Clicked page ${i} with cursor:`, cursor);
            onPageChange(i, cursor);
          }}
          disabled={loading}
          className={`px-3 py-1 text-sm rounded border ${
            currentPage === i
              ? 'bg-blue-600 text-white border-blue-600'
              : 'hover:bg-gray-100 text-gray-700 border-gray-300'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {i}
        </button>
      );
    }

    console.log('Generated buttons:', buttons.length);
    return buttons;
  };

  // 总是显示分页器，即使只有一页
  if (actualMaxKnownPage === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        {/* Items info and page size selector */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            {totalCount ? (
              `Showing ${startItem}-${endItem} of ${totalCount} ${itemName}`
            ) : (
              `Showing ${startItem}-${endItem} ${itemName}`
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <label htmlFor="pageSize" className="text-sm text-gray-600">
              Show:
            </label>
            <select
              id="pageSize"
              value={pageSize}
              onChange={(e) => onPageSizeChange(parseInt(e.target.value))}
              disabled={loading}
              className="select-styled border border-gray-300 rounded-md px-3 py-1.5 text-sm disabled:opacity-50"
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-sm text-gray-600">per page</span>
          </div>
        </div>

        {/* Pagination controls */}
        {actualMaxKnownPage > 0 && (
          <div className="flex items-center gap-2">
            {/* Previous page */}
            <button
              onClick={() => onPageChange(currentPage - 1, cursors[currentPage - 2])}
              disabled={currentPage === 1 || loading}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Previous page"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>

            {/* Page numbers */}
            <div className="flex items-center gap-1">
              {renderPageButtons()}
            </div>

            {/* Next page */}
            <button
              onClick={() => onPageChange(currentPage + 1, cursors[currentPage])}
              disabled={currentPage >= actualMaxKnownPage || loading}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Next page"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
      
      {/* Pagination info */}
      {actualMaxKnownPage > 0 && (
        <div className="mt-2 text-xs text-gray-500">
          Page {currentPage} of {actualMaxKnownPage}
          {totalCount && actualMaxKnownPage < Math.ceil(totalCount / pageSize) && (
            <span className="ml-1">(more pages available)</span>
          )}
          {!totalCount && actualMaxKnownPage > 1 && (
            <span className="ml-1">(more pages may be available)</span>
          )}
        </div>
      )}
    </div>
  );
};

export default CursorPagination;
