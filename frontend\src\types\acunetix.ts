// Acunetix API Types

export interface Config {
  baseUrl: string;
  apiKey: string;
  insecureSkipVerify: boolean;
  timeout: number;
}

export interface ConnectionStatus {
  connected: boolean;
  message: string;
  version?: string;
  build?: string;
}

export interface ScanAuthorization {
  url: string;
  content: string;
}

export interface Link {
  rel: string;
  href: string;
}

export interface Target {
  // Basic target information
  target_id: string;
  address: string;
  description: string;
  criticality: number;
  type: string;

  // Scan information
  last_scan_date?: string;
  last_scan_id?: string;
  last_scan_session_id?: string;
  last_scan_session_status?: string;

  // Security information
  severity_counts: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    info: number;
  };
  threat: number;
  threat_score: number;

  // Additional API fields
  scan_authorization?: ScanAuthorization;
  continuous_mode: boolean;
  links?: Link[];
  manual_intervention: boolean;
  verification?: string;

  // FQDN related fields
  fqdn?: string;
  fqdn_hash?: string;
  fqdn_status?: string;
  fqdn_tm_hash?: string;

  // System fields
  deleted_at?: string;
  default_scanning_profile_id?: string;
  default_overrides?: string;

  // Legacy fields for compatibility
  added_date?: string;
}

export interface NewTarget {
  address: string;
  description: string;
  criticality: number;
  type: string;
}

export interface TargetListResponse {
  targets: Target[];
  pagination: Pagination;
}

export interface Pagination {
  count: number;
  cursor_hash: string;
  cursors: (string | null)[];
  sort: string | null;
}

export interface ErrorResponse {
  code: number;
  message: string;
  details?: string;
}

export interface APIResponse<T> {
  data?: T;
  error?: ErrorResponse;
  success: boolean;
}

// Scan related types
export interface ScanSession {
  scan_session_id: string;
  status: string;
  start_date: string;
  end_date?: string;
  progress: number;
  threat_score: number;
  threat: number;
  event_level: number;
  severity_counts: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    info: number;
  };
  acusensor: boolean;
}

export interface Schedule {
  disable: boolean;
  start_date: string;
  time_sensitive: boolean;
}

export interface Scan {
  // Basic scan information
  scan_id: string;
  target_id: string;
  profile_id: string;
  profile_name: string;

  // Scan configuration
  ui_session_id: string;
  report_template_id: string[];
  max_scan_time: number;
  incremental: boolean;

  // Scan status and timing
  status: string;
  start_date?: string;
  next_run?: string;
  schedule: Schedule;

  // Session information
  current_session: ScanSession;
  previous_session?: ScanSession;

  // Target information
  target?: Target;
  criticality: number;
  manual_intervention: boolean;
}

export interface ScanListResponse {
  scans: Scan[];
  pagination: Pagination;
}

// Vulnerability related types
export interface Vulnerability {
  // Basic vulnerability information
  vuln_id: string;
  vt_name: string;
  severity: number;
  confidence: number;

  // Affected information
  affects_url: string;
  affects_detail: string;

  // Target information
  target_id: string;
  target_description: string;

  // Status and metadata
  status: string;
  last_seen: string;
  tags: string[];
  criticality: number;

  // Additional API fields
  app: string;
  archived: boolean;
  continuous: boolean;
  issue_id: string;
  issue_tracker_id: string;
  plugin_instance_id: string;
  vt_id: string;
  vt_created: string;
  vt_updated: string;
  api_operation_id: string;
  api_type: string;
}

export interface VulnerabilityListResponse {
  vulnerabilities: Vulnerability[];
  pagination: Pagination;
}
